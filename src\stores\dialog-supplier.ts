import { defineStore } from 'pinia'

export const useSupplierDialogStore = defineStore('dialog', {
  state: () => ({
    isOpen: false,
    isOpenInfo: false,
    mode: 'add',
  }),
  actions: {
    open(mode = 'add') {
      this.isOpen = true
      this.mode = mode
    },
    openInfo(mode = 'info') {
      this.isOpenInfo = true
      this.mode = mode
    },
    close() {
      this.isOpen = false
    },
    closeInfo() {
      this.isOpenInfo = false
    },
  },
})
