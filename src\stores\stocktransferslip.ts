import { acceptHMRUpdate, defineStore } from 'pinia'
import { stockTransFerSlipService } from 'src/services/stocktransferslipService'
import type { Stock } from 'src/types/stock'
import type { StockTransferSlip } from 'src/types/stockTransferSlip'
import type { StockTransferSlipDetails } from 'src/types/stockTransferSlipDetails'
import { ref } from 'vue'



export const useStockTransferSlipStore = defineStore('sts', {
  state: () => ({
    form: {} as StockTransferSlip,
    formSTSDetails: {} as StockTransferSlipDetails,
    products: {} as Stock,
    sts: [] as StockTransferSlip[],
    selectedFilter: ref('')
  }),


  actions: {
    async fetchAllStockTransferSlip() {
      try {
        this.sts = await stockTransFerSlipService.getAll()
      } catch (error) {
        console.error('Error fetching gr:', error)
      }
    },

    async fetchStockTransferSlipById(id: number) {
      try {
        const sts = await stockTransFerSlipService.getById(id)
        return (this.form = sts)
      } catch (error) {
        console.error(`Error fetching sts ${id}:`, error)
      }
    },
    async create(sts: StockTransferSlip) {
      try {
        const newSTS = await stockTransFerSlipService.create(sts)
        this.form = newSTS
      } catch (error) {
        console.error('Error creating sts:', error)
      }
    },

    async createFromSTO(stoId: number) {
      try {
        const newSTS = await stockTransFerSlipService.createFromSTO(stoId)
        this.form = newSTS
      } catch (error) {
        console.error('Error adding sts:', error)
      }
    },
    async update(updatedsts: StockTransferSlip) {
      try {
        const updated = await stockTransFerSlipService.update(updatedsts.id, updatedsts)
        if (updated) {
          this.form = updated
        }
      } catch (error) {
        console.error(`Error updating order ${updatedsts.id}:`, error)
      }
    },
    async updateSTSCreateBySTO(updatedsts: StockTransferSlip) {
      try {
        const updated = await stockTransFerSlipService.updateSTSCreateBySTO(updatedsts.id, updatedsts)
        if (updated) {
          this.form = updated
        }
      } catch (error) {
        console.error(`Error updating order ${updatedsts.id}:`, error)
      }
    },
    async removeSTS(stsId: number) {
      try {
        await stockTransFerSlipService.delete(stsId)
        this.sts = this.sts.filter((o) => o.id !== stsId)
      } catch (error) {
        console.error(`Error deleting gr ${stsId}:`, error)
      }
    },
    resetForm() {
      this.form = {} as StockTransferSlip
    },
    resetFormSTSDetails() {
      this.formSTSDetails = {} as StockTransferSlipDetails
    }
  },
})
// HMR Support (Hot Module Replacement)
if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useStockTransferSlipStore, import.meta.hot))
}
