/**
 * Location Service
 * Handles location validation and distance calculations for attendance system
 */

export interface LocationCoordinates {
  latitude: number;
  longitude: number;
  accuracy?: number;
}

export interface LocationValidationResult {
  isValid: boolean;
  distance: number;
  message: string;
}

export class LocationService {
  // Default allowed location (can be configured per organization)
  private static readonly DEFAULT_ALLOWED_LOCATION: LocationCoordinates = {
    latitude: 13.281294489182047,
    longitude: 100.9240488495316,
  };

  // Maximum allowed distance in meters (30 kilometers = 30000 meters)
  private static readonly MAX_ALLOWED_DISTANCE = 1000;

  /**
   * Calculate distance between two coordinates using Haversine formula
   * @param coord1 First coordinate
   * @param coord2 Second coordinate
   * @returns Distance in meters
   */
  public static calculateDistance(
    coord1: LocationCoordinates,
    coord2: LocationCoordinates
  ): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = (coord1.latitude * Math.PI) / 180; // φ, λ in radians
    const φ2 = (coord2.latitude * Math.PI) / 180;
    const Δφ = ((coord2.latitude - coord1.latitude) * Math.PI) / 180;
    const Δλ = ((coord2.longitude - coord1.longitude) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    const distance = R * c; // Distance in meters
    return Math.round(distance);
  }

  /**
   * Validate if user location is within allowed range
   * @param userLocation User's current location
   * @param allowedLocation Allowed location (optional, uses default if not provided)
   * @returns Validation result
   */
  public static validateLocation(
    userLocation: LocationCoordinates,
    allowedLocation?: LocationCoordinates
  ): LocationValidationResult {
    const targetLocation = allowedLocation || this.DEFAULT_ALLOWED_LOCATION;
    const distance = this.calculateDistance(userLocation, targetLocation);

    const isValid = distance <= this.MAX_ALLOWED_DISTANCE;

    return {
      isValid,
      distance,
      message: isValid
        ? `Location verified. Distance: ${distance}m from workplace.`
        : `Location validation failed. You are ${distance}m away from the workplace. Maximum allowed distance is ${this.MAX_ALLOWED_DISTANCE}m.`,
    };
  }

  /**
   * Validate location coordinates
   * @param location Location to validate
   * @returns True if coordinates are valid
   */
  public static isValidCoordinates(location: LocationCoordinates): boolean {
    return (
      location.latitude >= -90 &&
      location.latitude <= 90 &&
      location.longitude >= -180 &&
      location.longitude <= 180
    );
  }

  /**
   * Get default allowed location
   * @returns Default allowed location coordinates
   */
  public static getDefaultAllowedLocation(): LocationCoordinates {
    return { ...this.DEFAULT_ALLOWED_LOCATION };
  }

  /**
   * Get maximum allowed distance
   * @returns Maximum allowed distance in meters
   */
  public static getMaxAllowedDistance(): number {
    return this.MAX_ALLOWED_DISTANCE;
  }

  /**
   * Format distance for display
   * @param distance Distance in meters
   * @returns Formatted distance string
   */
  public static formatDistance(distance: number): string {
    if (distance < 1000) {
      return `${distance}m`;
    } else {
      return `${(distance / 1000).toFixed(1)}km`;
    }
  }
}
