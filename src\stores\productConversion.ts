import { defineStore, acceptHMRUpdate } from 'pinia'
import { ProductConversionService } from 'src/services/productConversionService'
import type { Product } from 'src/types/product'

// Define the ProductConversion type
export interface ProductConversion {
  id: number
  fromProduct: Product
  fromProductId: number
  toProduct: Product
  toProductId: number
  conversionRate: number
}

export const useProductConversionStore = defineStore('productConversion', {
  state: () => ({
    conversions: [] as ProductConversion[],
    currentConversion: null as ProductConversion | null,
    loading: false,
    error: null as string | null,
  }),

  actions: {
    async fetchAllConversions() {
      try {
        this.loading = true
        this.conversions = await ProductConversionService.getAll()
      } catch (error) {
        this.error = error instanceof Error ? error.message : 'Unknown error'
        console.error('Error fetching conversions:', error)
      } finally {
        this.loading = false
      }
    },

    async getConversionById(id: number) {
      try {
        this.loading = true
        this.currentConversion = await ProductConversionService.getById(id)
        return this.currentConversion
      } catch (error) {
        this.error = error instanceof Error ? error.message : 'Unknown error'
        console.error(`Error fetching conversion ${id}:`, error)
        return null
      } finally {
        this.loading = false
      }
    },

    async createConversion(conversion: Omit<ProductConversion, 'id'>) {
      try {
        this.loading = true
        const newConversion = await ProductConversionService.create(conversion)
        this.conversions.push(newConversion)
        return newConversion
      } catch (error) {
        this.error = error instanceof Error ? error.message : 'Unknown error'
        console.error('Error creating conversion:', error)
        return null
      } finally {
        this.loading = false
      }
    },

    async updateConversion(id: number, conversion: Partial<ProductConversion>) {
      try {
        this.loading = true
        const updatedConversion = await ProductConversionService.update(id, conversion)
        const index = this.conversions.findIndex((c) => c.id === id)
        if (index !== -1) {
          this.conversions[index] = updatedConversion
        }
        return updatedConversion
      } catch (error) {
        this.error = error instanceof Error ? error.message : 'Unknown error'
        console.error(`Error updating conversion ${id}:`, error)
        return null
      } finally {
        this.loading = false
      }
    },

    async deleteConversion(id: number) {
      try {
        this.loading = true
        await ProductConversionService.delete(id)
        this.conversions = this.conversions.filter((c) => c.id !== id)
        return true
      } catch (error) {
        this.error = error instanceof Error ? error.message : 'Unknown error'
        console.error(`Error deleting conversion ${id}:`, error)
        return false
      } finally {
        this.loading = false
      }
    },

    // ✅ BREAK PRODUCT: ส่ง id ไปใน URL, payload แยก
    async breakProduct(id: number, payload: { toId: number; quantity: number; branchId: number }) {
      try {
        this.loading = true
        return await ProductConversionService.breakProduct(id, payload)
      } catch (error) {
        this.error = error instanceof Error ? error.message : 'Unknown error'
        console.error(`Error breaking product with conversion ${id}:`, error)
        return null
      } finally {
        this.loading = false
      }
    },

    async combineProduct(id: number, quantity: number) {
      try {
        this.loading = true
        return await ProductConversionService.combineProduct(id, quantity)
      } catch (error) {
        this.error = error instanceof Error ? error.message : 'Unknown error'
        console.error(`Error combining product with conversion ${id}:`, error)
        return null
      } finally {
        this.loading = false
      }
    },

    clearError() {
      this.error = null
    },
  },
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useProductConversionStore, import.meta.hot))
}
