<template>
  <q-dialog v-model="SupplierdialogStore.isOpen">
    <q-card style="max-width: 1200px; width: 1000px; height: 610px">
      <q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">เพิ่มบริษัทจำหน่าย</div>
          <q-btn icon="close" @click="closeDialog" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section style="max-height: 600px" class="scroll">
        <!-- รายละเอียดบริษัท -->
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            รายละเอียดบริษัทจำหน่าย
          </div>
          <div class="shadow-2 container">
            <div class="row q-col-gutter-md">
              <div class="col-6">
                <div class="row q-mt-md">
                  <div class="col-4 flex flex-center text-right"></div>
                  <div class="col-8 row items-center">
                    <q-radio
                      v-model="store.form.isactive"
                      :val="true"
                      label="Active"
                      :true-value="true"
                      :false-value="false"
                      :disable="isReadOnly"
                      class="input-container q-ml-lg"
                      style="margin-left: 205px"
                    />
                  </div>
                </div>

                <div class="row q-mt-md">
                  <div class="col-4 flex flex-center text-right">รหัสบริษัท</div>
                  <div class="col-8">
                    <q-input
                      class="input-container"
                      v-model="store.form.supplier_number"
                      dense
                      borderless
                      :readonly="isReadOnly"
                      type="text"
                      @keypress="validateNumberInput"
                      @input="store.form.supplier_number = store.form.supplier_number.replace(/\D/g, '')"
                    />
                  </div>
                </div>
                <div class="row q-mt-md">
                  <div class="col-4 flex flex-center text-right">ชื่อบริษัท</div>
                  <div class="col-8">
                    <q-input
                      class="input-container"
                      v-model="store.form.name"
                      dense
                      borderless
                      :readonly="isReadOnly"
                       @input="validateTextInput('name')"
                    />
                  </div>
                </div>
                <div class="row q-mt-md">
                  <div class="col-4 mt-md text-center">ที่อยู่บริษัท</div>
                  <div class="col-8">
                    <q-input
                      class="input-container"
                      v-model="store.form.address"
                      dense
                      borderless
                      type="textarea"
                      :readonly="isReadOnly"
                    />
                  </div>
                </div>
              </div>

              <div class="col-6">
                <div class="row q-mt-md">
                  <div class="col-4 flex flex-center text-right">ประเภทบริษัท</div>
                  <div class="col-8 flex">
                    <q-radio
                      v-model="store.form.type.id"
                      :val="2"
                      label="บริษัทที่จำหน่าย"
                      :readonly="isReadOnly"
                    />
                    <q-radio
                      v-model="store.form.type.id"
                      :val="1"
                      label="บริษัทที่ผลิต"
                      class="q-ml-md"
                      :readonly="isReadOnly"
                    />
                  </div>
                </div>
                <div class="row q-mt-md">
                  <div class="col-4 flex flex-center text-right">เลขที่เสียภาษี</div>
                  <div class="col-8">
                    <q-input
                      class="input-container"
                      v-model="store.form.tax_number"
                      dense
                      borderless
                      :readonly="isReadOnly"
                      type="text"
                      @keypress="validateNumberInput"
                      @input="store.form.tax_number = store.form.tax_number.replace(/\D/g, '')"
                    />
                  </div>
                </div>
                <div class="row q-mt-md">
                  <div class="col-4 flex flex-center text-right">ชื่อผู้ติดต่อ</div>
                  <div class="col-8">
                    <q-input
                      class="input-container"
                      v-model="store.form.contact_name"
                      dense
                      borderless
                      :readonly="isReadOnly"
                       @input="validateTextInput('contact_name')"
                    />
                  </div>
                </div>
                <div class="row q-mt-md">
                  <div class="col-4 flex flex-center text-right">โทร</div>
                  <div class="col-8">
                    <q-input
                      class="input-container"
                      v-model="store.form.tel"
                      dense
                      borderless
                      :readonly="isReadOnly"
                      type="text"
                      @keypress="validateNumberInput"
                      @input="store.form.tel = store.form.tel.replace(/\D/g, '')"                      
                    />
                  </div>
                </div>
                <div class="row q-mt-md">
                  <div class="col-4 flex flex-center text-right">แฟกซ์</div>
                  <div class="col-8">
                    <q-input
                      class="input-container"
                      v-model="store.form.fax"
                      dense
                      borderless
                      :readonly="isReadOnly"
                      type="text"
                      @keypress="validateNumberInput"
                      @input="store.form.fax = store.form.fax.replace(/\D/g, '')"  
                    />
                  </div>
                </div>
                <div class="row q-mt-md">
                  <div class="col-4 flex flex-center text-right">Email</div>
                  <div class="col-8">
                    <q-input
                      class="input-container"
                      v-model="store.form.email"
                      dense
                      borderless
                      :readonly="isReadOnly"
                    />
                  </div>
                </div>
              </div>
            </div>
            <q-card-actions align="center" style="margin-top: 15px">
              <q-btn class="btn-accept" dense flat label="บันทึก" @click="saveDialog" :disable="isSaveDisabled"/>

              <q-btn class="btn-cancel" dense flat label="ยกเลิก" @click="closeDialog" />
            </q-card-actions>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { useDialogStore } from 'src/stores/dialog-store'
import { useSupplierDialogStore } from 'src/stores/dialog-supplier'
import { useSupplierStore } from 'src/stores/supplier'
import { computed, onMounted } from 'vue'

const SupplierdialogStore = useSupplierDialogStore()
const supplierStore = useSupplierStore()
const store = useSupplierStore()
const dialogStore = useDialogStore()
const isReadOnly = computed(() => dialogStore.mode === 'view')

onMounted(async () => {
  await supplierStore.loadSuppliers()
  store.resetForm()
})
const saveSupplier = async () => {
  try {
    if (store.form.id) {
      await store.updateSupplier(store.form)
    } else {
      await store.addSupplier(store.form)
    }

    return true
  } catch (error) {
    console.error('Error saving supplier:', error)
    return false
  }
}

const saveDialog = async () => {
  const success = await saveSupplier()
  if (success) {
    if (dialogStore.mode === 'add') {
      store.resetForm()
    }
    dialogStore.isOpen = false
    await store.fetchSupplier()
  }
}

const closeDialog = async () => {
  store.resetForm()
  dialogStore.close()
  await store.fetchSupplier()
}

const isSaveDisabled = computed(() => {
  return !store.form.supplier_number || 
         !store.form.name || 
         !store.form.tel || 
         !store.form.contact_name ||
         !store.form.type.id
})
const validateTextInput = (field: 'name' | 'contact_name') => {
  store.form[field] = store.form[field].replace(/[^a-zA-Zก-๙\s]/g, '')
}

const validateNumberInput = (event: KeyboardEvent) => {
  if (!/[0-9-]/.test(event.key)) {
    event.preventDefault();
  }
};



</script>

<style scoped>
.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.mini-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
}

.btn-cancel {
  background-color: #9c2c2c;
  margin-right: 10px;
}

.gap-container {
  margin-bottom: 20px;
}

.dialog-container {
  border-radius: 10px;
}

.input-container-v2 {
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  background-color: #83a7d8;
}

.input-container-v3 {
  padding-left: 10px;
  padding-right: 10px;
  margin-top: 5px;
  margin-bottom: 5px;
  border-radius: 5px;
  background-color: #d6e4f6;
  width: 150px;
}

.width-column {
  width: 300px;
}

.column-table {
  height: 40px;
  background-color: #294888;
  color: white;
}

.column-table-2 {
  height: 40px;
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border: 2px solid #294888;
}
</style>
