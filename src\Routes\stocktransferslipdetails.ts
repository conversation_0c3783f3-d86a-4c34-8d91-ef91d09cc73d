import { Router } from "express";
import { StockTransferSlipDetailsController } from "../Controllers/StockTransferSlipDetails";

const router = Router();
const controller = new StockTransferSlipDetailsController();

router.get("/", controller.getAll.bind(controller));
router.get("/:id", controller.getById.bind(controller));
router.post("/:stsId", controller.create.bind(controller));
router.post("/:stsId/update-from-sto", controller.updateSTSDetailsFromSTO.bind(controller));
router.put("/:id", controller.update.bind(controller));
router.delete("/:id", controller.delete.bind(controller));
router.put("/:id/updateMultiple", controller.updateMultiple.bind(controller));
export default router;
