import axios from 'axios'
import type { ProductConversion } from 'src/stores/productConversion'

// Optional: Create Axios instance with base URL
const api = axios.create({
  baseURL: 'http://localhost:3000/product-conversion',
})

export class ProductConversionService {
  static async getAll() {
    const res = await api.get('/')
    return res.data
  }

  static async getById(id: number) {
    const res = await api.get(`/${id}`)
    return res.data
  }

  static async create(conversion: Omit<ProductConversion, 'id'>) {
    const res = await api.post('/', conversion)
    return res.data
  }

  static async update(id: number, conversion: Partial<ProductConversion>) {
    const res = await api.put(`/${id}`, conversion)
    return res.data
  }

  static async delete(id: number) {
    const res = await api.delete(`/${id}`)
    return res.data
  }

  // ✅ ใช้ id แยกจาก payload ตามที่ frontend ต้องการ
  static async breakProduct(
    id: number,
    payload: { toId: number; quantity: number; branchId: number },
  ) {
    const res = await api.post(`/${id}/break`, payload)
    return res.data
  }

  static async combineProduct(id: number, quantity: number) {
    const res = await api.post(`/${id}/combine`, { quantity })
    return res.data
  }
}
