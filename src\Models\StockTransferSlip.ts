import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn } from "typeorm";
import { Branch } from "./Branch";
import { User } from "./User";
import { StockTransferOrderDetails } from "./StockTransferOrderDetails";
import { StockTransferOrder } from "./StockTransferOrder";
import { StockTransferSlipDetails } from "./StockTransferSlipDetails";
import { BranchReceive } from "./BranchReceive";

// sts_price: int
@Entity()
export class StockTransferSlip {
  @PrimaryGeneratedColumn()
  id!: number;

  @OneToOne(() => StockTransferOrder)
  @JoinColumn()
  sto!: StockTransferOrder;

  // @OneToOne(() => BranchReceive)
  // @JoinColumn()
  // br!: BranchReceive;

  @Column({ type: 'text' })
  code!: string;

  @Column({ type: 'text' })
  sto_code!: string;

  @Column({ type: 'text' })
  status!: string;

  @Column({ type: 'text', nullable: true })
  transfer_status!: string;

  @ManyToOne(() => Branch, { onDelete: "CASCADE" })
  @JoinColumn()
  source_branch!: Branch;

  @ManyToOne(() => Branch, { onDelete: "CASCADE" })
  @JoinColumn()
  destination_branch!: Branch;

  @Column({ type: "datetime" })
  transfer_date!: Date;

  @ManyToOne(() => User, { onDelete: "CASCADE" })
  @JoinColumn()
  user!: User;

  @Column({ type: 'text' })
  note!: string;

  @Column({ type: 'numeric' })
  sts_price!: number;

  @OneToMany(() => StockTransferSlipDetails, (sts_details) => sts_details.sts)
  sts_details!: StockTransferSlipDetails[];
}