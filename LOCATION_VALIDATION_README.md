# Location-Based Check-In/Check-Out Validation

This document describes the implementation of location-based validation for the pharmacy project's attendance system.

## Overview

The system now validates user location during check-in and check-out processes to ensure employees are within a 30-kilometer radius of the designated workplace location.

## Features

### Backend Implementation

1. **Database Schema Updates**

   - Added location fields to the `Attendance` model:
     - `check_in_latitude`, `check_in_longitude`, `check_in_accuracy`
     - `check_out_latitude`, `check_out_longitude`, `check_out_accuracy`

2. **Location Validation Service** (`src/Services/LocationService.ts`)

   - Haversine formula for distance calculation
   - Coordinate validation
   - Configurable allowed location and maximum distance (30km)

3. **Updated Attendance Controller**
   - Modified `clockIn` and `clockOut` methods to accept location data
   - Server-side location validation before processing attendance
   - Detailed error messages for location validation failures

### Frontend Implementation

1. **Geolocation Service** (`src/services/geolocationService.ts`)

   - Browser geolocation API integration
   - Permission handling
   - Error handling with user-friendly Thai messages

2. **Location Validation Composable** (`src/pages/User/composables/useLocationValidation.ts`)

   - Client-side location validation
   - Distance calculation
   - User location management

3. **Enhanced Map Integration**

   - Visual representation of allowed area (30km radius circle)
   - User location marker with validation status
   - Color-coded markers (green = valid, red = invalid)

4. **Updated Attendance Logic**
   - Location validation before API calls
   - Enhanced error notifications
   - Automatic location capture during check-in/check-out

## Configuration

### Default Settings

- **Allowed Location**: `13.281294489182047, 100.9240488495316` (สุขถาวรโอสถ สาขาบางแสน)
- **Maximum Distance**: 30000 meters (30 kilometers)
- **Geolocation Options**:
  - High accuracy enabled
  - 10-second timeout
  - 1-minute maximum age for cached location

### Customization

To change the allowed location or distance, update the constants in:

**Backend**: `src/Services/LocationService.ts`

```typescript
private static readonly DEFAULT_ALLOWED_LOCATION: LocationCoordinates = {
  latitude: 13.281294489182047,
  longitude: 100.9240488495316,
};
private static readonly MAX_ALLOWED_DISTANCE = 30000; // meters
```

**Frontend**: `src/pages/User/constants/index.ts`

```typescript
export const MAP_CONFIG = {
  DEFAULT_LOCATION: {
    lat: 13.281294489182047,
    lng: 100.9240488495316,
  },
  // ... other config
} as const
```

## User Experience

### Successful Check-In/Check-Out

1. User clicks check-in/check-out button
2. System requests location permission (if not granted)
3. System gets current location
4. System validates location is within 30km radius
5. System processes attendance with location data
6. User sees success notification

### Failed Location Validation

1. User clicks check-in/check-out button
2. System detects user is outside allowed area
3. System shows detailed error message with distance information
4. Check-in/check-out is prevented

### Error Scenarios

- **Permission Denied**: Clear message asking user to enable location access
- **Location Unavailable**: Instructions to check GPS/Wi-Fi connection
- **Timeout**: Suggestion to try again
- **Outside Range**: Specific distance information and allowed range

## Map Features

### Visual Elements

- **Blue Circle**: Allowed area (30km radius)
- **Red Marker**: Default workplace location
- **Green Marker**: User location (when valid)
- **Red Marker**: User location (when invalid)
- **Popup Information**: Status and distance details

### Interactive Features

- Click "Get Current Location" button to validate position
- Popup messages show validation status
- Real-time location updates

## API Changes

### Request Format

```json
{
  "userId": 123,
  "note": "optional note",
  "location": {
    "latitude": 13.281294489182047,
    "longitude": 100.9240488495316,
    "accuracy": 10
  }
}
```

### Error Response Format

```json
{
  "message": "Location validation failed",
  "details": "You are 35000m away from the workplace. Maximum allowed distance is 30000m.",
  "distance": 35000,
  "maxAllowedDistance": 30000
}
```

## Testing

Run the location validation tests:

```bash
npm run test src/pages/User/tests/locationValidation.test.ts
```

## Security Considerations

1. **Server-Side Validation**: All location validation is performed on both client and server
2. **Coordinate Validation**: Invalid coordinates are rejected
3. **Distance Calculation**: Uses precise Haversine formula
4. **Error Handling**: Graceful degradation when location services unavailable

## Browser Compatibility

- **Chrome/Edge**: Full support
- **Firefox**: Full support
- **Safari**: Full support
- **Mobile Browsers**: Full support with device GPS

## Troubleshooting

### Common Issues

1. **Location Permission Denied**

   - Solution: Guide user to enable location in browser settings

2. **Inaccurate Location**

   - Solution: Ensure GPS is enabled, try in open area

3. **Location Timeout**

   - Solution: Check internet connection, try again

4. **Outside Allowed Range**
   - Solution: Move closer to workplace or contact administrator

### Development

For development/testing, you can temporarily disable location validation by modifying the validation logic in the backend controller or frontend composable.
