import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn } from "typeorm";
import { Branch } from "./Branch";
import { User } from "./User";
import { StockTransferOrderDetails } from "./StockTransferOrderDetails";
import { StockTransferSlip } from "./StockTransferSlip";

// sts_price: int
@Entity()
export class StockTransferOrder {
  @PrimaryGeneratedColumn()
  id!: number;

  @OneToOne(() => StockTransferSlip, { onDelete: "CASCADE" })
  @JoinColumn()
  sts!: StockTransferSlip;

  @Column({ type: 'text' })
  code!: string;

  @ManyToOne(() => Branch, { onDelete: "CASCADE" })
  @JoinColumn()
  source_branch!: Branch;

  @ManyToOne(() => Branch, { onDelete: "CASCADE" })
  @JoinColumn()
  destination_branch!: Branch;

  @Column({ type: "datetime" })
  request_date!: Date;

  @ManyToOne(() => User, { onDelete: "CASCADE" })
  @JoinColumn()
  user!: User;

  @Column({ type: 'text' })
  note!: string;

  @Column({ type: 'text' })
  status!: string;

  @Column({ type: 'text', nullable: true })
  transfer_status!: string;

  @Column({ type: 'numeric' })
  sto_price!: number;

  @OneToMany(() => StockTransferOrderDetails, (sto_details) => sto_details.sto)
  sto_details!: StockTransferOrderDetails[];
}