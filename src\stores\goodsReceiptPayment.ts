import { acceptHMRUpdate, defineStore } from 'pinia'
import { goodsReceiptPaymentService } from 'src/services/goodReceiptPaymentService'
import type { PaymentGoodsReceipt } from 'src/types/paymentGoodsReceipt'

export const useGoodsReceiptPaymentStore = defineStore('paymentgr', {
  state: () => ({
    formPayment: {} as PaymentGoodsReceipt,
    payment: [] as PaymentGoodsReceipt[],
  }),

  actions: {
    async fetchAllGRPayment() {
      try {
        this.payment = await goodsReceiptPaymentService.getAll()
      } catch (error) {
        console.error('Error fetching gr:', error)
      }
    },
    async fetchById(id: number) {
      try {
        this.formPayment = await goodsReceiptPaymentService.getById(id)
      } catch (error) {
        console.error('Error fetching gr:', error)
      }
    },
    async fetchByGRId(id: number) {
      try {
        this.payment = await goodsReceiptPaymentService.getByGRId(id)
      } catch (error) {
        console.error('Error fetching gr:', error)
      }
    },
    async createByGR(grId: number, form: PaymentGoodsReceipt) {
      try {
        const payment = await goodsReceiptPaymentService.create(grId, form)
        this.formPayment = payment
        this.payment.push(payment) // เพิ่มรายการใหม่ลงในตาราง
      } catch (error) {
        console.error('Error adding gr:', error)
      }
    },
    async remove(id: number) {
      try {
        await goodsReceiptPaymentService.delete(id)
        this.payment = this.payment.filter((o) => o.id !== id)
      } catch (error) {
        console.error(`Error deleting gr ${id}:`, error)
      }
    },
    resetForm() {
      this.formPayment = {} as PaymentGoodsReceipt
    },
  },
})
// HMR Support (Hot Module Replacement)
if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useGoodsReceiptPaymentStore, import.meta.hot))
}
