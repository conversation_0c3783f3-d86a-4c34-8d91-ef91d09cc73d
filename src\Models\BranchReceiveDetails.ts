import { Column, <PERSON><PERSON><PERSON>, <PERSON>in<PERSON><PERSON><PERSON>n, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn } from "typeorm";
import { Branch } from "./Branch";
import { User } from "./User";
import { StockTransferOrderDetails } from "./StockTransferOrderDetails";
import { StockTransferOrder } from "./StockTransferOrder";
import { StockTransferSlipDetails } from "./StockTransferSlipDetails";
import { StockTransferSlip } from "./StockTransferSlip";
import { BranchReceive } from "./BranchReceive";
import { Product } from "./Product";
@Entity()
export class BranchReceiveDetails {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne(() => BranchReceive, { onDelete: "CASCADE" })
  @JoinColumn()
  br!: BranchReceive;

  @ManyToOne(() => StockTransferSlipDetails)
  @JoinColumn()
  sts_details!: StockTransferSlipDetails;

  @ManyToOne(() => Product, { onDelete: "CASCADE" })
  @JoinColumn()
  product!: Product;

  @Column({ type: 'numeric' })
  receive_quantity!: number;

  @Column({ type: 'text' })
  lot_number!: string;

}