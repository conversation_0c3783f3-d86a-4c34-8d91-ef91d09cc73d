import { api } from 'src/boot/axios'
import type { user } from 'src/types/user'

export class UserService {
  // อัปเดตวันหยุดประจำสัปดาห์ของ user
  static async updateDayOff(userId: number, dayOff: string) {
    try {
      const res = await api.put(`${this.path}/${userId}`, { day_off: dayOff })
      console.log('Updated day_off:', res.data)
      return res.data
    } catch (error) {
      console.error('Failed to update day_off:', error)
      throw error
    }
  }
  static path = 'user'

  // istungk์ istung login
  static async login(name: string, password: string) {
    const res = await api.post('auth/login', { name, password })
    console.log('User logged in:', res.data)

    // Log user image path if available
    if (res.data && res.data.id) {
      try {
        // Get user image using the dedicated endpoint
        const imageUrl = await this.getUserImageById(res.data.id)
        console.log('User image URL from API:', imageUrl)

        // Add the image URL to the user data if found
        if (imageUrl) {
          res.data.processedImageUrl = imageUrl
        }
      } catch (error) {
        console.error('Error fetching user image:', error)
      }
    }

    return res.data
  }

  // istungk์ istung getAll
  static async getAll() {
    const res = await api.get(this.path)
    console.log('user :', res.data)
    return res.data
  }

  // istungk์ istung getUserInfo
  static async getUserInfo(userId: number) {
    try {
      const res = await api.get(`${this.path}/${userId}`)
      return res.data
    } catch (error) {
      console.error('Failed to fetch user data:', error)
      throw error
    }
  }

  // istungk์ istung getUserImage - returns full image URL
  static getUserImageUrl(imagePath: string) {
    // If no image path is provided, return null
    if (!imagePath) {
      console.log('No image path provided')
      return null
    }

    // Log the incoming image path
    console.log('Original image path:', imagePath)

    // If the path is already a full URL, return it
    if (imagePath.startsWith('http')) {
      const fullUrl = imagePath
      console.log('Using full URL:', fullUrl)
      return fullUrl
    }

    // Special case for default image
    if (imagePath === 'noimage.png') {
      return null // Return null to trigger the fallback icon
    }

    // Get base URL without trailing slash
    const baseURL = api.defaults.baseURL
      ? api.defaults.baseURL.endsWith('/')
        ? api.defaults.baseURL.slice(0, -1)
        : api.defaults.baseURL
      : 'http://localhost:3000'

    // For user images, construct the specific path
    if (imagePath && !imagePath.startsWith('/')) {
      const userImageUrl = `${baseURL}/images/user/${imagePath}`
      console.log('Constructed user image URL:', userImageUrl)
      return userImageUrl
    }

    // Otherwise, construct the full URL using the API base URL
    const defaultUrl = `${baseURL}${imagePath.startsWith('/') ? '' : '/'}${imagePath}`
    console.log('Constructed default URL:', defaultUrl)
    return defaultUrl
  }

  // istungk์ istung filterUsers
  static async filterUsers(search: string = '', filter: string = '', role: string = '') {
    try {
      const res = await api.post(`${this.path}/filter`, { search, filter, role })
      console.log('Filtered users:', res.data)
      return res.data
    } catch (error) {
      console.error('Failed to filter users:', error)
      throw error
    }
  }

  // Get user image using the dedicated API endpoint
  static async getUserImageById(userId: number) {
    try {
      const res = await api.get(`${this.path}/image/${userId}`)
      console.log('User image data:', res.data)

      if (res.data && res.data.imagePath) {
        // Process the image path from the API response
        return this.getUserImageUrl(res.data.imagePath)
      }
      return null
    } catch (error) {
      console.error('Failed to fetch user image:', error)
      return null
    }
  }

  // Add new user
  static async addUser(userData: Partial<user>) {
    try {
      const res = await api.post(this.path, userData)
      console.log('User added:', res.data)
      return res.data
    } catch (error) {
      console.error('Failed to add user:', error)
      throw error
    }
  }

  // Update existing user
  static async updateUser(userData: Partial<user>) {
    try {
      const res = await api.put(`${this.path}/${userData.id}`, userData)
      console.log('User updated:', res.data)
      return res.data
    } catch (error) {
      console.error('Failed to update user:', error)
      throw error
    }
  }

  // Delete user
  static async deleteUser(userId: number) {
    try {
      const res = await api.delete(`${this.path}/${userId}`)
      console.log('User deleted:', userId)
      return res.data
    } catch (error) {
      console.error('Failed to delete user:', error)
      throw error
    }
  }

  // Upload user image
  static async uploadUserImage(userId: number, imageFile: File) {
    try {
      const formData = new FormData()
      formData.append('image', imageFile)
      formData.append('userId', userId.toString())

      // Try the primary upload endpoint
      const res = await api.post(`${this.path}/upload-image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
      console.log('Image uploaded:', res.data)
      return res.data
    } catch (error: unknown) {
      console.error('Failed to upload image:', error)

      // If it's a 404 error, the endpoint doesn't exist
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { status?: number } }
        if (axiosError.response?.status === 404) {
          console.warn('Image upload endpoint not available. Skipping image upload.')
          return { success: false, message: 'Image upload endpoint not available' }
        }
      }

      throw error
    }
  }

  // Alternative method: Add user with image data included
  static async addUserWithImage(userData: Partial<user>, imageFile?: File) {
    try {
      const userDataWithImage = { ...userData }

      // If image file is provided, convert to base64 and include in user data
      if (imageFile) {
        try {
          const base64Image = await this.convertFileToBase64(imageFile)
          userDataWithImage.image = base64Image
        } catch (imageError) {
          console.warn('Failed to process image, creating user without image:', imageError)
        }
      }

      const res = await api.post(this.path, userDataWithImage)
      console.log('User added with image:', res.data)
      return res.data
    } catch (error) {
      console.error('Failed to add user with image:', error)
      throw error
    }
  }

  // Helper method to convert file to base64
  static convertFileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        if (typeof reader.result === 'string') {
          resolve(reader.result)
        } else {
          reject(new Error('Failed to convert file to base64'))
        }
      }
      reader.onerror = () => reject(new Error('Failed to read file'))
      reader.readAsDataURL(file)
    })
  }
}
