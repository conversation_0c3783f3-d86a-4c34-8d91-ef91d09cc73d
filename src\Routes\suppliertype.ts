import { Router } from "express";
import { SupplierTypeController } from "../Controllers/suppliertype";

const router = Router();
const controller = new SupplierTypeController();

router.get("/", controller.getAll.bind(controller));
router.get("/:id", controller.getById.bind(controller));
router.post("/", controller.create.bind(controller));
router.put("/:id", controller.update.bind(controller));
router.delete("/:id", controller.delete.bind(controller));

export default router;
