import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, <PERSON><PERSON><PERSON>, OneToMany, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from "typeorm";
import { Supplier } from "./Supplier";
import { User } from "./User";
import { PurchaseOrderItem } from "./PurchaseOrderItem";
import { Branch } from "./Branch";

@Entity()
export class PurchaseOrder {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: "text", unique: true })
  code!: string;

  @ManyToOne(() => Supplier, { onDelete: "CASCADE" })
  @JoinColumn()
  supplier!: Supplier;

  @ManyToOne(() => Branch, { onDelete: "CASCADE" })
  @JoinColumn()
  branch!: Branch;

  @Column({ type: "text" })
  contact!: string;

  @Column({ type: "text" })
  address!: string;

  @Column({ type: "date" })
  date!: Date;

  @ManyToOne(() => User)
  @JoinColumn()
  user!: User;

  @Column({ type: "real", default: 0 })
  po_total!: number;

  // @Column({ type: "real", default: 0 })
  // po_total_v2!: number;

  @Column({ type: "float", default: 7.00, nullable: true })
  vat_percent!: number; // เปอร์เซ็นต์ภาษี เช่น 7

  @Column({ type: "float", default: 0 })
  tax!: number; // เงินภาษี

  @Column({ type: "float", default: 0 })
  tax_total!: number; //รวมภาษี

  @Column({ type: "text" })
  status!: string;

  @Column({ type: "datetime" })
  order_date!: Date;

  @Column({ type: "float", default: 0 }) //จำนวนส่วนลดท้ายบิล
  order_discount!: number;

  @Column({ type: "text", nullable: true })
  note!: string;

  @Column({ type: "float", default: 0, nullable: true })
  order_total!: number;

  @Column({ type: "float", default: 0, nullable: true })
  receive_total!: number;

  @Column({ type: "text", default: 'ไม่รวมภาษี', nullable: true })
  product_price_tax!: string; //ราคาสินค้า รวมภาษี/ไม่รวมภาษี

  @Column({ type: "text", default: 'ก่อนภาษี', nullable: true })  //ส่วนลดท้ายบิลก่อนภาษี/หลังภาษี
  order_discount_tax!: string;

  @Column({ type: "text" })
  receive_status!: string;

  @OneToMany(() => PurchaseOrderItem, (purchaseOrderItem) => purchaseOrderItem.purchase_order)
  purchase_order_items!: PurchaseOrderItem[];

}