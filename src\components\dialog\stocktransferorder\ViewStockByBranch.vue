<template>
  <q-dialog v-model="isOpen">
    <q-card style="max-width: 1100px; width: 500px">
      <q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">จำนวนสินค้าทุกสาขา</div>
          <q-btn icon="close" @click="closeDialog" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section style="overflow: auto; max-height: 80vh">
        <div class="gap-container">
          <div class="row items-center q-gutter-md">
            <div class="search-container">
              <SearchProductDialogComponent v-model="stockStore.searchTextSummary" placeholder="ค้นหา" />
            </div>
          </div>
        </div>
        <!--รายการสินค้า-->
        <div class="gap-container">
          <div class="shadow-2">
            <q-table flat class="body-table" :rows="stockStore.stocksSummary" :columns="columns" row-key="id"
              :pagination="pagination" :rows-per-page-options="[]">
              <template v-slot:body-cell-index="props">
                <q-td :props="props">
                  {{ props.rowIndex + 1 }}
                </q-td>
              </template>
            </q-table>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>


</template>
<script setup lang="ts">
import SearchProductDialogComponent from 'src/components/searchProductDialogComponent.vue'
import { useStockStore } from 'src/stores/stock'
import { ref, watch, computed } from 'vue'
import type { QTableColumn } from 'quasar'

// Props and emits for v-model support
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'product-added': []
}>()

// Computed property for v-model
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const stockStore = useStockStore()
const pagination = ref({
  rowsPerPage: 12,
})

// Watch for dialog opening to fetch initial data
watch(
  () => props.modelValue,
  async (newValue) => {
    console.log(stockStore.formforGR.product)
    if (newValue) {
      if (stockStore.formforGR.product && stockStore.formforGR.product.id) {
        stockStore.selectedProductSummary = String(stockStore.formforGR.product.id) || ''
        await stockStore.fetchAllStockByFilterSummary()
      }
    }
  }
);

watch(
  [
    () => stockStore.searchTextSummary,
  ],
  async () => {
    await stockStore.fetchAllStockByFilterSummary()

  }
);


const columns = <QTableColumn[]>[
  {
    name: 'index',
    label: 'ลำดับ',
    field: 'index',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'branch_name',
    label: 'สาขา',
    field: (row) => row.branch_name,
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'total_remaining',
    label: 'คงเหลือ',
    field: (row) => row.total_remaining,
    align: 'left' as const,
  }
]

const closeDialog = () => {
  stockStore.resetStocksSummary()
  isOpen.value = false
}



</script>
<style scoped>
.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.mini-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  margin-top: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
}

.btn-cancel {
  background-color: #9c2c2c;
  margin-right: 10px;
}

.gap-container {
  margin-bottom: 20px;
}

.dialog-container {
  border-radius: 10px;
}



.column-table {
  height: 40px;
  background-color: #294888;
  color: white;
}

.radio-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-left: 20px;
}

.q-radio {
  margin-right: 15px;
}

.custom-table {
  border-radius: 10px;
  overflow: hidden;
}

:deep(.q-table thead tr) {
  background-color: #294888;
  color: #deecff;
}

.body-table {
  background-color: #deecff;
}

.search-container {
  flex: 1;
  display: flex;
  align-items: center;
}

.filter-container {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}
</style>
