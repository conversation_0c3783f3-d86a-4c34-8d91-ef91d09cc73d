import { Router } from "express";
import { LeaveRequestController } from "../Controllers/LeaveRequest";

const router = Router();
const controller = new LeaveRequestController();

router.post("/create", (req, res) => controller.create(req, res));
router.get("/user/:userId", (req, res) => controller.getUserLeaveRequests(req, res));
router.post("/update-day-off", (req, res) => controller.updateDayOff(req, res));
router.get("/all", (req, res) => controller.getAllLeaveRequests(req, res));

export default router;
