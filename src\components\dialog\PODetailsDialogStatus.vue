<template>
  <q-dialog v-model="dialogPO.isOpen2" maximized>
    <q-card style="max-width: 1100px; width: 1100px">
      <q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">ใบรายการสั่งซื้อสินค้า</div>
          <q-btn icon="close" @click="closeDialog()" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section style="overflow: auto; max-height: 80vh">
        <div class="row">
          <div class="col-4">
            <div class="gap-container">
              <div class="text-white shadow-2 container-headerhalf row items-center">รหัสใบ PO</div>
              <div class="shadow-2 containerhalf">
                <div v-if="store.form" class="text-po">{{ store.form.code }}</div>
              </div>
            </div>
          </div>
          <div class="col-8">
            <div class="gap-container">
              <div class="gap-container-left">
                <div class="text-white shadow-2 container-headerhalf2 row items-center">
                  <span>อนุมัติรายการสั่งซื้อ</span>
                </div>
                <div class="shadow-2 containerhalf2 flex-container">
                  <q-btn @click="updateStatus('ดำเนินการ')" label="อนุมัติ" flat class="status-Sbotton" />
                  <q-btn @click="updateStatus('ยกเลิก')" label="ไม่อนุมัติ" flat class="status-Cbotton" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- รายละเอียดบริษัท -->
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            บริษัทจำหน่ายสินค้า
          </div>
          <div class="shadow-2 container">
            <div class="row">
              <div class="col-lg-12 row">
                <div class="col-1 q-mt-md">บริษัท</div>
                <div class="col-11">
                  <q-select v-model="store.form.supplier.name" :options="suppliers" option-value="id"
                    option-label="label" dense borderless class="input-container" emit-value map-options readonly
                    @update:model-value="
                      (value) => (store.form.supplier = { ...store.form.supplier, id: value })
                    ">
                  </q-select>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-12 row" style="margin-top: 10px">
                <div class="col-1 q-mt-md">ชื่อผู้ติดต่อ</div>
                <div class="col-5">
                  <q-input class="input-container" style="margin-right: 10px" v-model="store.form.contact" dense
                    readonly borderless />
                </div>
                <div class="col-1 q-mt-md text-center">ที่อยู่</div>
                <div class="col-5">
                  <q-input class="input-container" v-model="store.form.address" dense borderless readonly
                    type="textarea" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- การสั่งซื้อ -->
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            รายการสั่งซื้อสินค้า
          </div>
          <div class="shadow-2 container">
            <div class="row justify-between">
              <div class="col-11 row">
                <div class="col-1 q-mt-md">เลขที่</div>
                <q-input dense borderless class="input-container col-3" v-model="store.form.id"
                  style="width: 224px; height: 40px" readonly />
                <div class="col-1 q-mt-sm" style="margin-left: 40px">สถานะ</div>
                <q-radio keep-color v-model="store.form.status" val="เตรียมรายการ" label="เตรียมรายการ" color="orange"
                  style="color: orange" size="sm" disable />
                <q-radio keep-color v-model="store.form.status" val="ดำเนินการ" label="ดำเนินการ" color="blue"
                  style="color: royalblue" size="sm" disable />
                <q-radio keep-color v-model="store.form.status" val="เสร็จสมบูรณ์" label="เสร็จสมบูรณ์" color="green"
                  style="color: green" size="sm" disable />
                <q-radio keep-color v-model="store.form.status" val="ยกเลิก" label="ยกเลิก" color="red"
                  style="color: red" size="sm" disable />
              </div>
              <div class="width-column">
                <div class="col-lg-12 row" style="margin-top: 10px" justify-content: space-between>
                  <div class="col-3 q-mt-md">วันที่</div>
                  <q-input dense borderless class="input-container col-9" v-model="formattedDate" readonly />

                  <div class="col-lg-12 row" style="margin-top: 10px">
                    <div class="col-3 q-mt-md">พนักงาน</div>
                    <q-select class="input-container col-9" v-model="store.form.user.name" :options="users" readonly
                      option-value="name" option-label="label" dense borderless emit-value map-options
                      @update:model-value="
                        (value) => (store.form.user = { ...store.form.user, id: value })
                      ">
                    </q-select>
                  </div>

                  <div class="col-lg-12 row" style="margin-top: 10px">
                    <div class="col-3 q-mt-md">รวมเงิน</div>
                    <q-input dense borderless class="input-container col-9" readonly v-model="store.form.po_total" />
                    <div class="col-12 row" style="margin-top: 10px">
                      <div class="col-3 q-mt-md">ภาษี</div>
                      <q-input dense borderless class="input-container col-3" readonly v-model="store.form.tax" />

                      <div class="col-xl-1 q-mt-md" style="margin-left: 10px">เงินภาษี</div>
                      <q-input dense borderless class="input-container col-3" readonly v-model="store.form.tax_total"
                        style="margin-left: 15px" />
                    </div>
                  </div>
                </div>
              </div>
              <div class="col width-column" style="margin-left: 40px">
                <div class="row" style="margin-top: 10px">
                  <div class="col-xl-1 q-mt-md">วันที่สั่งสินค้า</div>
                  <div class="col-2">
                    <q-input dense borderless class="input-container" v-model="formattedOrderDate" readonly
                      style="margin-left: 10px; width: 255px">
                      <q-icon name="event" size="md" color="black" style="cursor: pointer; margin-top: 5px">
                        <q-popup-proxy transition-show="scale" transition-hide="scale">
                          <q-date v-model="formattedOrderDate" mask="DD/MM/YYYY" color="teal" />
                        </q-popup-proxy>
                      </q-icon>
                    </q-input>
                  </div>
                </div>
                <div class="row" style="margin-top: 10px">
                  <div class="col-xl-1 q-mt-md">หมายเหตุ</div>
                  <div class="col-4">
                    <q-input dense borderless class="input-container" v-model="store.form.note" readonly
                      style="margin-left: 30px; width: 255px; height: 90px" />
                  </div>
                </div>
                <div class="row" style="margin-top: 10px">
                  <div class="col-xl-1 q-mt-md">รวมจำนวน</div>
                  <div class="col-4" style="margin-left: 15px">
                    <q-input dense borderless class="input-container" v-model="store.form.order_total" readonly
                      label="สั่ง" style="margin-left: 10px" />
                  </div>
                  <!-- <div class="col-1"></div> -->
                  <div class="col-4" style="margin-left: 15px">
                    <q-input dense borderless class="input-container" v-model="store.form.receive_total" readonly
                      label="รับ" style="margin-left: 10px" />
                  </div>
                </div>
                <div class="row" style="margin-top: 10px">
                  <div class="col-12">
                    <div class="input-container" style="width: 345px; height: 40px; text-align: left">
                      รับสินค้า
                      <q-radio keep-color v-model="store.form.product_price_tax" val="รอรับ" label="รอรับ"
                        style="font-size: 12px; color: orange" size="sm" color="orange" disable />
                      <q-radio keep-color v-model="store.form.product_price_tax" val="รับครบแล้ว" label="รับครบแล้ว"
                        style="font-size: 12px; color: green" size="sm" color="green" disable />
                      <q-radio keep-color v-model="store.form.product_price_tax" val="ยกเลิกรับ" label="ยกเลิกรับ"
                        style="font-size: 12px; color: red" size="sm" color="red" disable />
                    </div>
                    <!-- <q-input dense borderless class="input-container" v-model="store.form.order_total" label="สั่ง"
                        style="margin-left: 10px" /> -->
                  </div>
                </div>
              </div>
              <div class="col-1 width-column">
                <div class="row">
                  <div class="col-xl-1" style="font-size: 12px">ส่วนลดท้ายบิล</div>
                  <div class="col-1 mini-container width-column">
                    <div class="col-2">
                      <div class="row items-center">
                        <div style="font-size: 13px; margin-left: 10px">จำนวนส่วนลด</div>
                        <q-input dense borderless class="input-container-v3" v-model="store.form.order_discount"
                          readonly style="margin-left: 10px" />
                      </div>
                    </div>
                  </div>
                  <div class="row" style="margin-bottom: 20px">
                    <div class="col-xl-2 q-mt-md" style="font-size: 12px; margin-top: 5px">
                      สินค้ามีภาษี
                    </div>
                    <div class="col-2 mini-container width-column">
                      <div class="col-4" style="font-size: 13px">
                        <div class="col-xl-2 q-mt-md">ราคาสินค้า</div>
                        <div class="col text-center">
                          <q-radio v-model="store.form.product_price_tax" val="รวมภาษี" label="รวมภาษี" color="grey-7"
                            disable />
                          <q-radio v-model="store.form.product_price_tax" val="ไม่รวมภาษี" label="ไม่รวมภาษี"
                            color="grey-7" disable />
                        </div>
                        <div class="col-xl-2 q-mt-md">ส่วนลดท้ายบิล</div>
                        <div class="col text-center" style="margin-bottom: 10px">
                          <q-radio v-model="store.form.order_discount_tax" val="ก่อนภาษี" label="ก่อนภาษี"
                            color="grey-7" disable />
                          <q-radio v-model="store.form.order_discount_tax" val="หลังภาษี" label="หลังภาษี"
                            color="grey-7" disable />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--รายการสินค้า-->
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">รายการสินค้า</div>
          <div class="shadow-2 container-table">
            <q-table flat class="body-table" :rows="store.orderItems" :columns="columns" row-key="id">
              <template v-slot:bottom-row>
                <q-tr class="total-row">
                  <q-td colspan="4" class="text-right total-label">
                    <div class="row justify-end items-center">
                      รวม
                    </div>
                  </q-td>

                  <q-td class="text-right total-value">
                    <div class="row justify-end items-center no-wrap">
                      <span>{{ totalPrice }}</span>
                      <span class="q-ml-sm">บาท</span>
                    </div>
                  </q-td>
                </q-tr>
              </template>
              <template v-slot:body-cell-index="props">
                <q-td :props="props">
                  {{ props.rowIndex + 1 }}
                </q-td>
              </template>
            </q-table>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { usePurchaseOrderStore } from 'src/stores/purchaseorder'
import { useSupplierStore } from 'src/stores/supplier'
import { computed, onMounted, ref } from 'vue'
import type { QTableColumn } from 'quasar'
import { date } from 'quasar'
import { useDialogPODetails } from 'src/stores/dialog-po-details'
import { watch } from 'vue'
import { useStockStore } from 'src/stores/stock'
import { useProductStore } from 'src/stores/product'
import { useUserStore } from 'src/stores/userStore'

const dialogPO = useDialogPODetails()
const store = usePurchaseOrderStore()
const supplierStore = useSupplierStore()
const stockStore = useStockStore()
const product = useProductStore()
onMounted(async () => {
  await store.fetchOrdersItem()
  store.currentStatus = store.form.status
  await stockStore.fetchAllStock()
  await product.fetchProducts()
  await supplierStore.loadSuppliers()
  await product.fetchProductGroups()
  await product.fetchSpecialReportGroups()
})

watch(
  () => dialogPO.mode,
  (newMode) => {
    if (newMode === 'add') {
      console.log('add')
      store.orderItems = []
    }
  },
)

const totalPrice = computed(() => {
  return store.orderItems.reduce((sum, item) => sum + item.total_price, 0).toFixed(2)
})

const columns = <QTableColumn[]>[
  {
    name: 'index',
    label: 'ลำดับ',
    field: '',
    align: 'left' as const,
    sortable: false,
  },
  {
    name: 'product_name',
    label: 'ชื่อสินค้า',
    field: (row) => (row.product ? row.product.product_name : ''),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'quantity',
    label: 'จำนวนที่สั่ง',
    field: (row) => row.quantity,
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'unit_price',
    label: 'ราคา',
    field: (row) => row.unit_price.toFixed(2),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'total_price',
    label: 'รวมเงิน',
    field: (row) => row.total_price.toFixed(2),
    align: 'left' as const,
    sortable: true,
  },
]

function resetOrderItems() {
  store.orderItems = JSON.parse(JSON.stringify(store.orderItems))
}

const saveOrder = async () => {
  try {
    if (store.deletedIds.length > 0) {
      console.log('delete')
      await store.removeOrderItem()
      store.resetFormItem()
    }
    const poId = store.form.id
    if (!poId) {
      console.log('Adding new PO...')
      await store.addOrder(store.form)
      await store.addProduct()
    } else {
      console.log('Updating existing PO...')
      await store.updateOrder(store.form)
      await store.fetchOrders()
    }
    store.editOrderItems = []

    return true
  } catch (error) {
    console.error('Error saving order:', error)
    return false
  }
}

const suppliers = computed(() =>
  supplierStore.suppliers
    .filter((supplier) => supplier.type?.id === 2)
    .map((supplier) => ({
      id: supplier.id,
      label: supplier.name,
    })),
)

const updateStatus = async (status: string) => {
  console.log('Updating status to:', status)
  store.currentStatus = status // อัปเดตสถานะที่เลือก
  await saveOrder() // บันทึกคำสั่งซื้อ
  await closeDialog() // ปิด dialog และโหลดข้อมูลใหม่
}

const filteredOrders = ref(store.orders)

const closeDialog = async () => {
  console.log('Closing dialog and resetting form...')
  resetOrderItems()
  store.resetForm()
  store.resetFormItem()
  store.editOrderItems = []

  dialogPO.isOpen2 = false
  try {
    await store.fetchOrders()
    filteredOrders.value = store.orders
    await store.fetchOrdersItem()
  } catch (error) {
    console.error('Error fetching data:', error)
  }
}

const formattedOrderDate = computed({
  get() {
    // ตรวจสอบว่า store.form.order_date มีค่าไหม
    const dateValue = store.form.order_date ? store.form.order_date : new Date()
    return date.formatDate(dateValue, 'DD/MM/YYYY') // แปลงจาก Date เป็น string ในรูปแบบ dd/mm/yyyy
  },
  set(value: string) {
    const parts = value.split('/') // แยกค่าโดยใช้ '/'

    if (parts.length === 3) {
      const [day, month, year] = parts
      const newDate = new Date(`${year}-${month}-${day}`) // แปลงกลับเป็น Date
      store.form.order_date = newDate // อัพเดทค่าใน store
    } else {
      store.form.order_date = new Date() // หากรูปแบบไม่ถูกต้อง, ใช้วันที่ปัจจุบัน
    }
  },
})

const formattedDate = computed({
  get() {
    return date.formatDate(store.form.date, 'DD/MM/YYYY') // แปลงจาก Date เป็น string ในรูปแบบ YYYY-MM-DD
  },
  set(value: string) {
    const newDate = new Date(value) // แปลงจาก string กลับเป็น Date
    store.form.date = newDate // อัพเดทค่าใน store
  },
})
const userStore = useUserStore()
const users = computed(() =>
  userStore.users.map((supplier) => ({
    id: supplier.id,
    label: supplier.name,
  })),
)
</script>

<style scoped>
.status-Sbotton {
  border-radius: 8px;
  width: 200px;
  background-color: #439e62;
}

.status-Cbotton {
  border-radius: 8px;
  width: 200px;
  background-color: #b53638;
}

.text-po {
  margin-left: 15px;
  font-size: 30px;
}

.total-row {
  background-color: #83a7d8;
  color: black;
  font-weight: bold;
}

/* ทำให้ "รวม" อยู่ชิดขวาสุดของคอลัมน์ก่อนตัวเลข */
.total-label {
  text-align: right;
}

/* ทำให้ตัวเลขอยู่ชิดขวาสุดของเซลล์ */
.total-value {
  text-align: right;
  padding-right: 16px;
  /* ปรับระยะห่างให้ดูดี */
}

.total-row td {
  text-align: right;
  /* จัดข้อความให้ชิดขวาทุกช่อง */
  padding: 12px;
  /* ปรับระยะห่างให้ดูดีขึ้น */
}

.container-table {
  background-color: #294888;
  padding: 0;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  overflow: hidden;
}

.container-header-table {
  background-color: #294888;
  /* สีของหัวข้อ */
  color: white;
  padding: 16px;
  font-size: 16px;
  font-weight: bold;
}

.body-table {
  background-color: #deecff;
}

:deep(.q-table thead tr) {
  background-color: #83a7d8;
}

.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.containerhalf {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  width: 350px;
  height: 100px;
}

.containerhalf2 {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  width: 680px;
  height: 100px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.container-headerhalf {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  width: 350px;
  height: 55px;
}

.container-headerhalf2 {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  width: 680px;
  height: 55px;
}

.mini-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.btn-add {
  background-color: #ffffff;
  color: #000000;
  border-radius: 5px;
  margin-left: 20px;
  font-size: 13px;
}

.btn-accept {
  background-color: #36b54d;
  width: 150px;
  height: 40px;
  border-radius: 10px;
  margin-right: 10px;
}

.btn-print {
  background-color: #83a7d8;
  width: 150px;
  height: 40px;
  border-radius: 10px;
  margin-right: 10px;
}

.gap-container {
  margin-bottom: 20px;
}

.gap-container-left {
  margin-left: 20px;
}

.dialog-container {
  border-radius: 10px;
}

.input-container-v2 {
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  background-color: #83a7d8;
}

.input-container-v3 {
  padding-left: 10px;
  padding-right: 10px;
  margin-top: 5px;
  margin-bottom: 5px;
  border-radius: 5px;
  background-color: #d6e4f6;
  width: 150px;
}

.width-column {
  width: 300px;
}

.column-table {
  height: 40px;
  background-color: #294888;
  color: white;
}

.column-table-2 {
  height: 40px;
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border: 2px solid #294888;
}

/* .custom-radio {
  padding: 10px;
  color: white !important;
} */

.flex-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  /* ปรับระยะห่างระหว่าง radio */
  padding: 10px;
}

.status-text {
  margin-left: 300px;
  margin-right: 30px;
}

.custom-radio .q-radio__inner--truthy {
  background-color: currentColor !important;
  opacity: 1;
}

.custom-radio .q-radio__bg {
  color: currentColor !important;
  /* เปลี่ยนสีขอบให้ตรงกับ radio */
}

.custom-radio .q-radio__label {
  color: black !important;
  /* สีตัวอักษร */
  font-weight: bold;
  margin-left: 5px;
}
</style>
