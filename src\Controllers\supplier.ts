import { Request, Response } from "express";
import { AppDataSource } from "../Config/db"; // นำเข้าการตั้งค่าการเชื่อมต่อ
import { Supplier } from "../Models/Supplier"; // นำเข้าโมเดล Supplier
import { SupplierType } from "../Models/SupplierType"; // นำเข้าโมเดล SupplierType

export class SupplierController {
  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const supplierRepository = AppDataSource.getRepository(Supplier);
      const suppliers = await supplierRepository.find({ relations: ["type"] }); // โหลดความสัมพันธ์
      res.status(200).json(suppliers);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const supplierRepository = AppDataSource.getRepository(Supplier);
      const supplier = await supplierRepository.findOne({
        where: { id: Number(id) },
        relations: ["type"] // โหลดความสัมพันธ์
      });
      if (supplier) {
        res.status(200).json(supplier);
      } else {
        res.status(404).json({ message: "Supplier not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getAllByFilter(req: Request, res: Response): Promise<void> {
    const { search, filter, type } = req.body;

    try {
      const supplierRepository = AppDataSource.getRepository(Supplier);

      const query = supplierRepository
        .createQueryBuilder("supplier")
        .leftJoinAndSelect("supplier.type", "supplier_type")
        .where("1=1");

      if (search) {
        query.andWhere(
          `(
            supplier.supplier_number LIKE :search
            OR supplier.name LIKE :search
            OR supplier.tel LIKE :search
            OR supplier.contact_name LIKE :search
          )`,
          { search: `%${search}%` }
        );
      }

      if (filter && search) {
        query.andWhere(`supplier.${filter} LIKE :search`, {
          search: `%${search}%`,
        });
      }

      if (type) {
        query.andWhere("supplier_type.id = :type", { type });
      }

      const suppliers = await query.getMany();
      res.status(200).json(suppliers);
    } catch (error) {
      console.error("Error filtering suppliers:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }



  public async create(req: Request, res: Response): Promise<void> {
    const newSupplier = AppDataSource.getRepository(Supplier).create(req.body);
    try {
      const savedSupplier = await AppDataSource.getRepository(Supplier).save(newSupplier);
      res.status(201).json(savedSupplier);
    } catch (error) {
      res.status(400).json({ message: "Error creating Supplier" });
    }
  }

  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const supplierRepository = AppDataSource.getRepository(Supplier);
    try {
      const supplier = await supplierRepository.findOne({
        where: { id: Number(id) },
        relations: ["type"] // โหลดความสัมพันธ์
      });
      if (supplier) {
        supplierRepository.merge(supplier, req.body);
        const updatedSupplier = await supplierRepository.save(supplier);
        res.status(200).json(updatedSupplier);
      } else {
        res.status(404).json({ message: "Supplier not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating Supplier" });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const supplierRepository = AppDataSource.getRepository(Supplier);
    try {
      const result = await supplierRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "Supplier not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }
}