import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { SupplierType } from "../Models/SupplierType";

export class SupplierTypeController {
  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const supplierTypeRepository = AppDataSource.getRepository(SupplierType);
      const supplierTypes = await supplierTypeRepository.find();
      res.status(200).json(supplierTypes);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const supplierTypeRepository = AppDataSource.getRepository(SupplierType);
      const supplierType = await supplierTypeRepository.findOne({ where: { id: Number(id) } });
      if (supplierType) {
        res.status(200).json(supplierType);
      } else {
        res.status(404).json({ message: "SupplierType not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async create(req: Request, res: Response): Promise<void> {
    const newSupplierType = AppDataSource.getRepository(SupplierType).create(req.body);
    try {
      const savedSupplierType = await AppDataSource.getRepository(SupplierType).save(newSupplierType);
      res.status(201).json(savedSupplierType);
    } catch (error) {
      res.status(400).json({ message: "Error creating SupplierType" });
    }
  }

  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const supplierTypeRepository = AppDataSource.getRepository(SupplierType);
    try {
      const supplierType = await supplierTypeRepository.findOne({ where: { id: Number(id) } });
      if (supplierType) {
        supplierTypeRepository.merge(supplierType, req.body);
        const updatedSupplierType = await supplierTypeRepository.save(supplierType);
        res.status(200).json(updatedSupplierType);
      } else {
        res.status(404).json({ message: "SupplierType not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating SupplierType" });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const supplierTypeRepository = AppDataSource.getRepository(SupplierType);
    try {
      const result = await supplierTypeRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "SupplierType not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }
}
