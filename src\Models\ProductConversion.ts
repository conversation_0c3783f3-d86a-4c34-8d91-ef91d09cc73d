import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn } from 'typeorm';
import { Product } from './Product';

@Entity()
export class ProductConversion {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne(() => Product)
  @JoinColumn({ name: 'fromProductId' })
  fromProduct!: Product;

  @Column()
  fromProductId!: number;

  @ManyToOne(() => Product)
  @JoinColumn({ name: 'toProductId' })
  toProduct!: Product;

  @Column()
  toProductId!: number;

  @Column({ type: 'int' })
  conversionRate!: number; // e.g., 1 box = 24 packs
}
