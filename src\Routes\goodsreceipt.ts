import { Router } from "express";
import { GoodsReceiptController } from "../Controllers/GoodsReceipt";

const router = Router();
const controller = new GoodsReceiptController();

router.get("/", controller.getAll.bind(controller));
router.get("/:id", controller.getById.bind(controller));
router.get("/status/:status", controller.getByStatus.bind(controller));
router.post("/branch/:branchId/distributor/:id", controller.getProductByDistrbutor.bind(controller));
router.post("/", controller.createFromDistributor.bind(controller));
router.post("/createByPO/:poId", controller.createGRByPO.bind(controller));
router.put("/:id", controller.update.bind(controller));
router.delete("/:id", controller.delete.bind(controller));
router.post("/filter", controller.getAllByFilter.bind(controller));

export default router;
