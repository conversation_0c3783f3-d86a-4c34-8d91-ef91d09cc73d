import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { PurchaseOrder } from "../Models/PurchaseOrder";
import { User } from "../Models/User";
import { Branch } from "../Models/Branch";
import { StockTransferOrder } from "../Models/StockTransferOrder";
import { Stock } from "../Models/Stock";
import { StockTransferSlip } from "../Models/StockTransferSlip";
import { StockTransferSlipDetails } from "../Models/StockTransferSlipDetails";

export class StockTransferSlipController {

  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const stsRepository = AppDataSource.getRepository(StockTransferSlip);
      const sts = await stsRepository.find({
        relations: ["sto", "user", "source_branch", "destination_branch", "sts_details"]
      });
      res.status(200).json(sts);
    } catch (error) {
      console.error("Error in getAll:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const stsRepository = AppDataSource.getRepository(StockTransferSlip);
      const sts = await stsRepository.findOne({
        where: { id: Number(id) },
        relations: ["sto", "user", "source_branch", "destination_branch", "sts_details", "sts_details.product"]
      });
      if (sts) {
        res.status(200).json(sts);
      } else {
        res.status(404).json({ message: "STS not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getProductByBranch(req: Request, res: Response): Promise<void> { //source branch
    const { id } = req.params;
    try {
      const stockRepository = AppDataSource.getRepository(Stock);
      const stsRepository = AppDataSource.getRepository(StockTransferSlip)
      const sts = await stsRepository.findOne({
        where: { id: Number(id) },
        relations: ["sto", "user", "source_branch", "destination_branch", "sts_details", "sts_details.product"]
      });
      if (!sts) {
        res.status(404).json({ message: "STS not found" });
        return;
      }
      const product = await stockRepository.find({
        where: { branch: { id: sts?.source_branch.id } },
        relations: ["product"]
      })
      if (product) {
        res.status(200).json({
          branch: sts.source_branch,
          products: product,
        });
      } else {
        res.status(404).json({ message: "Product not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getAllByFilter(req: Request, res: Response): Promise<void> {
    const { search, filter, startDate, endDate } = req.body;

    try {
      const purchaseOrderRepository = AppDataSource.getRepository(PurchaseOrder);

      const query = purchaseOrderRepository
        .createQueryBuilder("purchase_order")
        .leftJoinAndSelect("purchase_order.supplier", "supplier")
        .leftJoinAndSelect("purchase_order.user", "user")
        .where("1=1");

      // กรองตามคำค้นหาที่ส่งมา
      if (search) {
        query.andWhere(
          `(
            purchase_order.code LIKE :search
            OR user.name LIKE :search
            OR purchase_order.status LIKE :search
          )`,
          { search: `%${search}%` }
        );
      }

      if (filter && search) {
        query.andWhere(`purchase_order.${filter} LIKE :search`, {
          search: `%${search}%`,
        });
      }

      if (startDate) {
        query.andWhere("DATE(purchase_order.date) >= :startDate", {
          startDate: startDate,
        });
      }

      if (endDate) {
        query.andWhere("DATE(purchase_order.date) <= :endDate", {
          endDate: endDate,
        });
      }

      const purchaseOrders = await query.getMany();
      res.status(200).json(purchaseOrders);
    } catch (error) {
      console.error("Error filtering purchase orders:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async create(req: Request, res: Response): Promise<void> {
    const userRepository = AppDataSource.getRepository(User);
    const branchRepository = AppDataSource.getRepository(Branch);
    const stsRepository = AppDataSource.getRepository(StockTransferSlip);

    const {
      source_branch,
      destination_branch,
      transfer_date,
      user,
      note,
      sts_price = 0,
      sts_details = [] // ถ้าไม่ได้ส่งมาก็ให้เป็น array ว่าง
    } = req.body;

    try {
      const userEntity = await userRepository.findOneByOrFail({ id: user.id });
      const sourceBranch = await branchRepository.findOneByOrFail({ id: source_branch.id });
      const destinationBranch = await branchRepository.findOneByOrFail({ id: destination_branch.id });

      // แปลงวันที่เป็น YYYYMMDD
      const formattedDate = new Date(transfer_date).toISOString().slice(0, 10).replace(/-/g, "");

      // ดึง ID ล่าสุด เพื่อใช้ในการ gen code
      const lastSts = await stsRepository.find({
        order: { id: "DESC" },
        take: 1
      });

      const lastId = lastSts.length > 0 ? lastSts[0].id + 1 : 1;

      // สร้างรหัส
      const generatedCode = `STS-${formattedDate}-${lastId}`;

      // สร้าง STO ใหม่
      const newSts = stsRepository.create({
        code: generatedCode,
        sto_code: '',
        source_branch: sourceBranch,
        destination_branch: destinationBranch,
        transfer_date,
        user: userEntity,
        note,
        status: 'เตรียมรายการ', // กำหนดสถานะเริ่มต้น
        sts_price,
        sts_details, // จะเป็น [] ถ้าไม่ได้ส่งมา
        transfer_status: 'จัดเตรียมสินค้า'
      });

      const savedSTO = await stsRepository.save(newSts);

      res.status(201).json(savedSTO);
    } catch (error) {
      console.error("Error creating STS:", error);
      res.status(500).json({ message: "Failed to create STS", error });
    }
  }

  public async createSTSBySTO(req: Request, res: Response): Promise<void> {
    const stsRepository = AppDataSource.getRepository(StockTransferSlip);
    const stoRepository = AppDataSource.getRepository(StockTransferOrder);
    const stockRepository = AppDataSource.getRepository(Stock);
    const { stoId } = req.params
    const sto = await stoRepository.findOneOrFail({
      where: { id: Number(stoId) },
      relations: ["user", "source_branch", "destination_branch", "sto_details", "sto_details.product"]
    });
    try {
      // แปลงวันที่เป็น YYYYMMDD
      const formattedDate = new Date().toISOString().slice(0, 10).replace(/-/g, "");

      // ดึง ID ล่าสุด เพื่อใช้ในการ gen code
      const lastSts = await stsRepository.find({
        order: { id: "DESC" },
        take: 1
      });

      const lastId = lastSts.length > 0 ? lastSts[0].id + 1 : 1;

      // สร้างรหัส
      const generatedCode = `STS-${formattedDate}-${lastId}-STO-${sto.code}`;
      let count = 0
      //หา quantity ของstock เพื่อมา update transfer_status
      await Promise.all(
        sto.sto_details.map(async (detail) => {
          const lotStock = await stockRepository.findOneOrFail({
            where: {
              branch: { id: sto.destination_branch.id },
              product: { id: detail.product.id }
            },
            relations: ["product", "branch"]
          });
          if (lotStock.remaining === 0) {
            count += 1;
          } else if (lotStock.remaining < detail.quantity) {
            count += 1;
          }
        })
      );
      let newStatus = '';
      if (count > 0) {
        newStatus = 'จัดเตรียมสินค้า';
      } else {
        newStatus = 'รอส่งรายการสินค้า';
      }
      await stoRepository.update(sto.id, {
        transfer_status: newStatus
      });

      // สร้าง STS ใหม่
      const newSts = stsRepository.create({
        sto: sto,
        code: generatedCode,
        sto_code: sto.code,
        source_branch: sto.source_branch,
        destination_branch: sto.destination_branch,
        transfer_date: new Date(),
        user: sto.user,
        note: sto.note,
        status: 'เตรียมรายการ', // กำหนดสถานะเริ่มต้น
        sts_price: 0,
        transfer_status: newStatus
      });
      // บันทึกลง DB เพื่อให้ได้ newSts.id
      const savedSTS = await stsRepository.save(newSts);

      // แปลง detail และผูกกลับหา parent
      const stsDetailRepository = AppDataSource.getRepository(StockTransferSlipDetails);


      const stsDetails = await Promise.all(
        sto.sto_details.map(async (detail) => {
          const lotStock = await stockRepository.findOneOrFail({
            where: {
              branch: { id: sto.destination_branch.id },
              product: { id: detail.product.id }
            },
            relations: ["product", "branch"]
          });
          let status = ''
          let sts_quantity = 0
          if (lotStock.remaining === 0) {
            status = 'ไม่มีจำนวนส่ง'
            sts_quantity = 0
          } else if (lotStock.remaining >= detail.quantity) {
            status = 'จำนวนส่งพอดี'
            sts_quantity = detail.quantity
          } else {
            status = 'จำนวนส่งน้อยกว่า'
            sts_quantity = lotStock.remaining
          }
          return stsDetailRepository.create({
            product: detail.product,
            sto_quantity: detail.quantity,
            sts_quantity: sts_quantity,
            sts_details_status: status,
            lot_number: lotStock.lot_number,
            sto_details: detail,
            sts: newSts
          });
        })
      );

      // save ทั้ง array
      await stsDetailRepository.save(stsDetails);
      savedSTS.sts_details = stsDetails;

      //upate sts in sto
      await stoRepository.update(sto.id, {
        sts: savedSTS
      });

      const response = {
        ...savedSTS,
        sts_details: stsDetails.map(detail => ({
          id: detail.id,
          product: detail.product,
          sto_quantity: detail.sto_quantity,
          sts_quantity: detail.sts_quantity,
          sts_details_status: detail.sts_details_status,
          lot_number: detail.lot_number,
          // sts: { id: detail.sts.id }
        }))
      };

      res.status(201).json(response);

      // res.status(201).json(savedSTS);
    } catch (error) {
      console.error("Error creating STS:", error);
      res.status(500).json({ message: "Failed to create STS", error });
    }
  }

  public async updateSTS(req: Request, res: Response): Promise<void> { //update อันที่ create sto-> sts
    const { id } = req.params; //
    const stsRepository = AppDataSource.getRepository(StockTransferSlip);
    const stoRepository = AppDataSource.getRepository(StockTransferOrder);

    try {
      const sts = await stsRepository.findOneOrFail({ where: { id: Number(id) }, relations: ["sto", "sts_details", "sts_details.product"] });
      const sto = await stoRepository.findOneOrFail({ where: { id: sts.sto.id } })
      if (sts.status === 'ดำเนินการ') {
        //update sto
        await stoRepository.update(sto.id, {
          transfer_status: 'สนญ.รับทราบ'
        });

      } else if (sts.status === 'เตรียมรายการ') {
        let count = 0
        // แปลง sto_details → sts_details
        await Promise.all(
          sts.sts_details.map(async (detail) => {
            if (detail.sts_details_status === 'ไม่มีจำนวนส่ง') {
              count += 1;
            }
          })
        );
        //ีupate sto
        if (count > 0) {
          await stoRepository.update(sto.id, {
            transfer_status: 'จัดเตรียมสินค้า'
          });
        } else {
          await stoRepository.update(sto.id, {
            transfer_status: 'รอส่งรายการสินค้า'
          });
        }
      } // เดี๋ยวมาต่อ
      stsRepository.merge(sts, req.body);
      const updatedSts = await stsRepository.save(sts);
      res.status(200).json(updatedSts);
    } catch (error) {
      res.status(400).json({ message: "Error updating STS", error });
    }
  }

  public async update(req: Request, res: Response): Promise<void> { //update ใบ STS manual
    const { id } = req.params;
    const stsRepository = AppDataSource.getRepository(StockTransferSlip);
    try {
      const sts = await stsRepository.findOne({ where: { id: Number(id) } });
      if (sts) {
        stsRepository.merge(sts, req.body);
        const updatedSts = await stsRepository.save(sts);
        res.status(200).json(updatedSts);
      } else {
        res.status(404).json({ message: "STS not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating STS", error });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const stsRepository = AppDataSource.getRepository(StockTransferSlip);
    try {
      const result = await stsRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "STS not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }
}