import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { PurchaseOrder } from "../Models/PurchaseOrder";
import { Supplier } from "../Models/Supplier";
import { User } from "../Models/User";
import { Branch } from "../Models/Branch";
import { StockTransferOrder } from "../Models/StockTransferOrder";
import { Stock } from "../Models/Stock";
import { GoodsReceipt } from "../Models/GoodsReceipt";
import { GoodsReceiptDetails } from "../Models/GoodsReceiptDetails";
import { PaymentGoodsReceipt } from "../Models/PaymentGoodsReceipt";

export class PaymentGoodsReceiptController {

  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const paymentGrRepository = AppDataSource.getRepository(PaymentGoodsReceipt);
      const paymentGr = await paymentGrRepository.find({
        relations: ["gr", "user"]
      });
      res.status(200).json(paymentGr);
    } catch (error) {
      console.error("Error in getAll:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const paymentGrRepository = AppDataSource.getRepository(PaymentGoodsReceipt);
      const paymentGr = await paymentGrRepository.findOne({
        where: { id: Number(id) }, relations: ["gr", "user", "receivedBy"]

      });
      if (paymentGr) {
        res.status(200).json(paymentGr);
      } else {
        res.status(404).json({ message: "GR Payment not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getByGrId(req: Request, res: Response): Promise<void> {
    const { grId } = req.params;
    try {
      const paymentGrRepository = AppDataSource.getRepository(PaymentGoodsReceipt);
      const paymentGr = await paymentGrRepository.find({
        where: { gr: { id: Number(grId) } }, relations: ["gr", "user", "receivedBy"]

      });
      if (paymentGr) {
        res.status(200).json(paymentGr);
      } else {
        res.status(200).json([{}]);
      }
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getAllByFilter(req: Request, res: Response): Promise<void> {
    const { search, filter, startDate, endDate } = req.body;

    try {
      const purchaseOrderRepository = AppDataSource.getRepository(PurchaseOrder);

      const query = purchaseOrderRepository
        .createQueryBuilder("purchase_order")
        .leftJoinAndSelect("purchase_order.supplier", "supplier")
        .leftJoinAndSelect("purchase_order.user", "user")
        .where("1=1");

      // กรองตามคำค้นหาที่ส่งมา
      if (search) {
        query.andWhere(
          `(
            purchase_order.code LIKE :search
            OR user.name LIKE :search
            OR purchase_order.status LIKE :search
          )`,
          { search: `%${search}%` }
        );
      }

      if (filter && search) {
        query.andWhere(`purchase_order.${filter} LIKE :search`, {
          search: `%${search}%`,
        });
      }

      if (startDate) {
        query.andWhere("DATE(purchase_order.date) >= :startDate", {
          startDate: startDate,
        });
      }

      if (endDate) {
        query.andWhere("DATE(purchase_order.date) <= :endDate", {
          endDate: endDate,
        });
      }

      const purchaseOrders = await query.getMany();
      res.status(200).json(purchaseOrders);
    } catch (error) {
      console.error("Error filtering purchase orders:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async create(req: Request, res: Response): Promise<void> { //createfromdistributor
    const paymentRepository = AppDataSource.getRepository(PaymentGoodsReceipt);
    const userRepository = AppDataSource.getRepository(User);
    const receiverRepository = AppDataSource.getRepository(Supplier);
    const grRepository = AppDataSource.getRepository(GoodsReceipt);
    const poRepository = AppDataSource.getRepository(PurchaseOrder);
    const stockRepository = AppDataSource.getRepository(Stock);
    const grDetailsRepository = AppDataSource.getRepository(GoodsReceiptDetails);
    const { grId } = req.params;
    const gr = await grRepository.findOne({ where: { id: Number(grId) }, relations: ["user", "po", "gr_details", "branch"] })
    const stock = await stockRepository.find({ where: { branch: { id: gr?.branch.id } }, relations: ["product", "branch"] })
    const grDetails = await grDetailsRepository.find({ where: { gr: { id: Number(grId) } }, relations: ["product"] })
    if (!gr) {
      res.status(404).json({ message: "GR Details not found" });
      return;
    }
    const {
      paid_amount,
      unpaid_amount,
      change_amount,
      payment_type,
      payment_date,
      cash_amount = 0.00,
      note = '',
      total_amount,
      receivedBy,
      user
    } = req.body;

    try {
      if (!user || !user.id) {
        res.status(400).json({ message: "Missing or invalid user in request body" });
        return;
      }
      if (!receivedBy || !receivedBy.id) {
        res.status(400).json({ message: "Missing or invalid receivedBy in request body" });
        return;
      }
      const userEntity = await userRepository.findOneByOrFail({ id: user.id })
      const receivedByEntity = await receiverRepository.findOneByOrFail({ id: receivedBy.id })
      // ดึง ID ล่าสุด เพื่อใช้ในการ gen code
      const lastGR = await grRepository.find({
        order: { id: "DESC" },
        take: 1
      });

      const lastId = lastGR.length > 0 ? lastGR[0].id + 1 : 1;

      // สร้างรหัส
      const generatedCode = `PYO-${gr?.id}-${lastId}`;
      const formattedDate = new Date().toISOString().slice(0, 10).replace(/-/g, "");

      // สร้าง STO ใหม่
      const newPayment = paymentRepository.create({
        code: generatedCode,
        user: userEntity,
        receivedBy: receivedByEntity,
        total_net: gr?.tax_total,
        paid_amount: paid_amount,
        unpaid_amount: unpaid_amount, // กำหนดสถานะเริ่มต้น
        change_amount: change_amount,
        payment_type: payment_type,
        payment_date: payment_date, // จะเป็น [] ถ้าไม่ได้ส่งมา
        cash_amount: cash_amount,
        note,
        total_amount,
        gr: gr
      });
      const savedPayment = await paymentRepository.save(newPayment);
      await grRepository.update(gr.id, {
        gr_total: paid_amount,
        // payment: [savedPayment]
      });
      if (gr.po && typeof gr.po.id === 'number') {
        const receiveTotal = gr.gr_details && gr.gr_details.length > 0
          ? gr.gr_details.reduce((sum, detail) => sum + detail.receive_quantity, 0)
          : 0;

        await poRepository.update(gr.po.id, {
          receive_status: 'รับครบแล้ว',
          receive_total: receiveTotal,
          status: 'เสร็จสมบูรณ์'
        });
        // Generate running numbers for lot_number if not exists
        let runningNumber = 1;

        // Find the highest existing lot number for today to continue the sequence
        const existingLotNumbers = await stockRepository
          .createQueryBuilder("stock")
          .where("stock.lot_number LIKE :pattern", { pattern: `LOT-${formattedDate}-%` })
          .getMany();

        if (existingLotNumbers.length > 0) {
          const maxNumber = Math.max(...existingLotNumbers.map(s => {
            const match = s.lot_number?.match(/LOT-\d{8}-(\d+)$/);
            return match ? parseInt(match[1]) : 0;
          }));
          runningNumber = maxNumber + 1;
        }

        // Update stock records with proper lot numbers and quantities
        for (const details of grDetails) {
          let status = ''
          let lot_number = `LOT-${formattedDate}-${runningNumber.toString().padStart(3, '0')}`;
          runningNumber++;

          if (details.total_receive_quantity >= details.product.stock_min) {
            status = 'สินค้าคงอยู่';
          } else if (details.total_receive_quantity < details.product.stock_min) {
            status = 'สินค้าใกล้หมด';
          } else {
            status = 'สินค้าหมด'
          }
          const newStock = await stockRepository.create({
            product: details.product,
            cost_unit: details.cost_unit,
            branch: gr.branch,
            remaining: details.total_receive_quantity,
            lot_number: lot_number,
            status: status,
            is_active: true
          });
          await grDetailsRepository.update(details.id, { lot_number_before: lot_number }); //update lot number in gr details
          await stockRepository.save(newStock);
        }
        //create new lot

        // if (stockItem.remaining == 0) {
        //   await stockRepository.update(stockItem.id, {
        //     is_active: false
        //   });
        // }
      }
      res.status(201).json(savedPayment);
    } catch (error) {
      console.error("Error creating payment:", error);
      res.status(500).json({ message: "Failed to create payment", error });
    }
  }


  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const paymentRepository = AppDataSource.getRepository(PaymentGoodsReceipt);
    try {
      const result = await paymentRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "Payment not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }
}