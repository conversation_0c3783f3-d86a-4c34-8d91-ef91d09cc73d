import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON>n, CreateDateColumn, UpdateDateColumn } from "typeorm";
import { User } from "./User";

@Entity()
export class WorkingSchedule {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne(() => User, user => user.workingSchedules, { onDelete: "CASCADE" })
  @JoinColumn()
  user!: User;

  @Column({ type: 'time', default: '09:00:00' })
  standard_check_in_time!: string;

  @Column({ type: 'time', default: '17:00:00' })
  standard_check_out_time!: string;

  @Column({ type: 'integer', default: 5 })
  late_threshold_minutes!: number;

  @Column({ type: 'integer', default: 30 })
  early_threshold_minutes!: number;

  @Column({ type: 'boolean', default: true })
  is_active!: boolean;

  @CreateDateColumn()
  created_at!: Date;

  @UpdateDateColumn()
  updated_at!: Date;
}
