import { Router } from "express";
import { StockTransferOrderController } from "../Controllers/StockTransferOrder";

const router = Router();
const controller = new StockTransferOrderController();

router.get("/", controller.getAll.bind(controller));
router.get("/:id", controller.getById.bind(controller));
router.get("/status/:status", controller.getByStatus.bind(controller));
router.get("/product/:id", controller.getProductByBranch.bind(controller));
router.post("/", controller.create.bind(controller));
router.put("/:id", controller.update.bind(controller));
router.delete("/:id", controller.delete.bind(controller));
router.post("/filter", controller.getAllByFilter.bind(controller));

export default router;
