<template>
  <q-dialog v-model="isOpen" maximized>
    <q-card style="max-width: 1100px">
      <q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">ร้องขอสินค้า</div>
          <q-btn icon="close" @click="closeDialog" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section style="overflow: auto; max-height: 80vh">
        <div class="col-lg-12 row">
          <div class="col-12 col-md-4 q-mt-sm order-1 order-md-1">
            <div class="gap-container">
              <div class="text-white shadow-2 container-headerhalf full-width row items-center">
                รหัสใบร้องขอสินค้า
              </div>
              <div class="shadow-2 containerhalf full-width">
                <div v-if="store.form" class="text-po">
                  {{ store.form.code }}
                </div>
              </div>
            </div>
          </div>
          <div class="col-12 col-md-8 q-mt-sm order-2 order-md-1">
            <div class="gap-container">
              <div class="gap-container-left">
                <div class="text-white shadow-2 container-headerhalf2 full-width row items-center">
                  <span>เปลี่ยนสถานะสินค้า</span>
                  <span class="status-text">สถานะสินค้า : {{ store.form.status }}</span>
                </div>
                <div class="shadow-2 containerhalf2 flex-container full-width">
                  <div class="row q-col-gutter-sm" style="font-size: 16px">
                    <q-radio v-model="store.form.status" val="เตรียมรายการ" label="เตรียมรายการ" color="orange"
                      class="custom-radio" size="lg" />
                    <q-radio v-model="store.form.status" val="ดำเนินการ" label="ดำเนินการ" color="blue"
                      class="custom-radio" />
                    <q-radio v-model="store.form.status" val="เสร็จสมบูรณ์" label="เสร็จสมบูรณ์" color="green"
                      class="custom-radio" />
                    <q-radio v-model="store.form.status" val="ยกเลิก" label="ยกเลิก" color="red" class="custom-radio" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- รายละเอียดบริษัท -->
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            รายละเอียดการสั่งโอน
          </div>
          <div class="shadow-2 container">
            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-1 q-mt-md" style="margin-left: 20px">เลขที่</div>
              <div class="col-12 col-md-4">
                <q-input class="input-container" v-model="store.form.id" dense borderless style="width: 310px"
                  readonly />
              </div>
              <!--สถานะ-->
              <div class="col-1 q-pr-md flex flex-center" style="margin-left: 10px">สถานะ</div>
              <div class="row q-col-gutter-sm">
                <q-radio keep-color v-model="store.form.status" val="เตรียมรายการ" label="เตรียมรายการ" color="orange"
                  style="color: orange" size="sm" disable />
                <q-radio keep-color v-model="store.form.status" val="ดำเนินการ" label="ดำเนินการ" color="blue"
                  style="color: royalblue" size="sm" disable />
                <q-radio keep-color v-model="store.form.status" val="เสร็จสมบูรณ์" label="เสร็จสมบูรณ์" color="green"
                  style="color: green" size="sm" disable />
                <q-radio keep-color v-model="store.form.status" val="ยกเลิก" label="ยกเลิก" color="red"
                  style="color: red" size="sm" disable />
              </div>
            </div>

            <!-- แถว: วันที่ + พนักงาน (ซ้าย) | หมายเหตุ (ขวา) -->
            <div class="row q-col-gutter-md">
              <!-- คอลัมน์ซ้าย: วันที่ + พนักงาน -->
              <div class="col-12 col-md-5" style="margin-top: 10px; margin-left: 10px">
                <!-- วันที่ -->
                <div class="row items-center q-gutter-sm q-mb-sm">
                  <div class="col-12 col-md-2 q-mt-md" style="margin-left: 18px">วันที่</div>
                  <div class="col-12 col-md-9">
                    <q-input dense borderless class="input-container" v-model="formattedOrderDate"
                      style="margin-left: 5px">
                      <q-icon name="event" size="md" color="black" style="cursor: pointer; margin-top: 5px">
                        <q-popup-proxy transition-show="scale" transition-hide="scale">
                          <q-date v-model="formattedOrderDate" mask="DD/MM/YYYY" color="teal" />
                        </q-popup-proxy>
                      </q-icon>
                    </q-input>
                  </div>
                </div>

                <!-- พนักงาน -->
                <div class="row items-center q-gutter-sm q-mb-sm">
                  <div class="col-12 col-md-2 q-mt-md" style="margin-left: 18px">พนักงาน</div>
                  <div class="col-12 col-md-9">
                    <q-input dense borderless class="input-container" v-model="store.form.user.name"
                      style="margin-left: 5px" readonly />
                  </div>
                </div>
              </div>
              <div class="col-12 col-md-6">
                <div class="row items-start q-gutter-sm" style="margin-top: 5px">
                  <div class="col-12 col-md-2 q-mt-md" style="margin-left: 20px">หมายเหตุ</div>
                  <div class="col-12 col-md-9">
                    <q-input class="input-container" v-model="store.form.note" dense borderless type="textarea"
                      style="width: 100%; margin-left: 20px" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- การสั่งซื้อ -->

        <div class="row">
          <div class="gap-container-half">
            <div class="text-white shadow-2 container-header row items-center">สาขาต้นทาง</div>
            <div class="shadow-2 container">
              <div class="row q-col-gutter-md">
                <div class="col-12 col-md-2 q-mt-md" style="margin-left: 20px">สาขา</div>
                <div class="col-12 col-md-9">
                  <q-input class="input-container col-9" v-model="store.form.source_branch.name" dense borderless
                    readonly />
                </div>
              </div>

              <div class="row q-col-gutter-md" style="margin-top: 10px">
                <div class="col-12 col-md-2 q-mt-md" style="margin-left: 20px">ชื่อผู้ติดต่อ</div>
                <div class="col-12 col-md-9">
                  <q-input class="input-container col-9" v-model="store.form.source_branch.name" dense borderless
                    type="text" style="width: 100%" readonly />
                </div>
              </div>

              <div class="row q-col-gutter-md" style="margin-top: 10px">
                <div class="col-12 col-md-2 q-mt-md" style="margin-left: 20px">ที่อยู่</div>
                <div class="col-12 col-md-9">
                  <q-input class="input-container" v-model="store.form.source_branch.address" dense borderless
                    type="textarea" style="width: 100%" readonly />
                </div>
              </div>
            </div>
          </div>
          <div class="gap-container-half" style="margin-left: 20px">
            <div class="text-white shadow-2 container-header row items-center">สาขาปลายทาง</div>
            <div class="shadow-2 container">
              <div class="row q-col-gutter-md">
                <div class="col-12 col-md-2 q-mt-md" style="margin-left: 20px">สาขา</div>
                <div class="col-12 col-md-9">
                  <q-input class="input-container col-9" v-model="store.form.destination_branch.name" dense borderless
                    readonly />
                </div>
              </div>

              <div class="row q-col-gutter-md" style="margin-top: 10px">
                <div class="col-12 col-md-2 q-mt-md" style="margin-left: 20px">ชื่อผู้ติดต่อ</div>
                <div class="col-12 col-md-9">
                  <q-input class="input-container col-9" v-model="store.form.destination_branch.name" dense borderless
                    type="text" style="width: 100%" readonly />
                </div>
              </div>

              <div class="row q-col-gutter-md" style="margin-top: 10px">
                <div class="col-12 col-md-2 q-mt-md" style="margin-left: 20px">ที่อยู่</div>
                <div class="col-12 col-md-9">
                  <q-input class="input-container" v-model="store.form.destination_branch.address" dense borderless
                    type="textarea" style="width: 100%" readonly />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            รายการสินค้าร้องขอ
          </div>
          <div class="shadow-2 container q-pa-md">
            <div class="row items-center q-gutter-sm">
              <!-- Label และ Input -->
              <div class="row items-center">
                <span class="q-mr-sm text-center" style="margin-left: 10px">Barcode </span>
                <q-input v-model="stockStore.searchTextDialog" dense borderless class="input-container-2"
                  input-style="padding-bottom: 5px" />
              </div>

              <!-- ปุ่ม 1 -->
              <q-btn label="ค้นหาสินค้า" class="btn-add-2" flat dense @click="openProductSTO" />
            </div>
            <div class="row items-center q-gutter-sm">
              <q-table flat class="body-table" :rows="store.stoDetails" :columns="columns" row-key="id"
                style="width: 100%; border-radius: 10px; overflow: hidden">
                <template v-slot:body-cell-actions="props">
                  <q-td :props="props" class="q-gutter-x-sm" style="min-width: 100px">
                    <q-btn icon="edit" padding="none" flat style="color: #e19f62" @click="editSTOdetails(props.row)" />
                    <q-btn icon="delete" padding="none" flat style="color: #b53638"
                      @click="deleteSTODetails(props.row)" />
                  </q-td>
                </template>
                <template v-slot:body-cell-index="props">
                  <q-td :props="props">
                    {{ props.rowIndex + 1 }}
                  </q-td>
                </template>
              </q-table>
            </div>
          </div>
        </div>
      </q-card-section>
      <q-card-actions align="right">
        <q-btn class="btn-accept" dense flat label="บันทึก" @click="saveDialog" />

        <q-btn class="btn-print" dense flat label="พิมพ์เอกสาร" @click="closeDialog" />
      </q-card-actions>
    </q-card>
  </q-dialog>
  <SearchPDSTO v-model="searchPDDialog"></SearchPDSTO>
  <EditSTOItem v-model="editSTOdetailsDialogOpen" :mode="modeEditSTOdetails"></EditSTOItem>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import type { QTableColumn } from 'quasar'
import { date } from 'quasar' // ใช้ quasar date utility หากต้องการจัดการวันที่
import { useStockTransferOrderStore } from 'src/stores/stocktransferorder'
import { useUserStore } from 'src/stores/userStore'
import { useAuthStore } from 'src/stores/authStore'
import { useBranchStore } from 'src/stores/branch'
import SearchPDSTO from './SearchPDSTO.vue'
import type { StockTransferOrderDetails } from 'src/types/stockTransferOrderDetails'
import { useStockStore } from 'src/stores/stock'
import EditSTOItem from './EditSTOItem.vue'

const store = useStockTransferOrderStore()
const userStore = useUserStore()
const authStore = useAuthStore()
const branchStore = useBranchStore()
const searchPDDialog = ref(false)
const stockStore = useStockStore()
const editSTOdetailsDialogOpen = ref(false)
const modeEditSTOdetails = ref('add')
const openProductSTO = () => {
  searchPDDialog.value = true
}

interface Props {
  modelValue: boolean
  mode?: string
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'edit',
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'item-updated': []
}>()

const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const closeDialog = () => {
  try {
    // Preserve essential data before closing
    const currentUser = authStore.currentUser || userStore.currentUser
    const currentBranch = store.form.source_branch || currentUser?.branch
    const currentUserData = store.form.user || currentUser

    isOpen.value = false

    // Reset form but preserve user and branch data to prevent undefined errors
    store.resetForm()
    store.resetFormSTODetails()

    // Restore essential data after reset
    if (currentUserData) {
      store.form.user = currentUserData
    }
    if (currentBranch) {
      store.form.source_branch = currentBranch
    }

    console.log('STO Dialog closed safely with preserved user/branch data')
  } catch (error) {
    console.error('Error in closeDialog:', error)
    // Fallback: just close the dialog without reset if there's an error
    isOpen.value = false
  }
}
const saveDialog = async () => {
  isOpen.value = false
  await store.update()
  await store.createSTODetails(store.stoDetails, store.form.id)
  await store.fetchSTOByStatus()
  store.resetForm()
  store.form.destination_branch = {
    id: 0,
    name: '',
    address: '',
  }
  // await store.fetchOrders()
}
onMounted(async () => {
  await userStore.fetchUsers()
  await branchStore.fetchAllBranch()
  const currentUser = computed(() => {
    return authStore.currentUser || userStore.currentUser
  })
  store.form.user = userStore.users.find((user) => user.id === currentUser.value?.id) ?? {
    id: 0,
    name: '',
    password: '',
    tel: '',
    role: '',
    hour_work: 0,
    sick_level: 0,
    personal_leave: 0,
    sick_leave_remaining: 0,
    personal_leave_remaining: 0,
    image: '',
    day_off: '',
    branch: {
      id: 0,
      name: '',
      address: '',
    },
  }
  console.log(store.form.user)
  store.form.source_branch = store.form.user.branch ?? {
    id: 0,
    name: '',
    address: '',
  }
})
watch(
  () => props.modelValue,
  async (newValue) => {
    console.log(store.form.destination_branch)
    if (newValue) {
      try {
        // Ensure user data is loaded
        await userStore.fetchUsers()
        await branchStore.fetchAllBranch()

        // Get current user safely
        const currentUser = authStore.currentUser || userStore.currentUser

        if (currentUser) {
          // Set user data if not already set
          if (!store.form.user || !store.form.user.id) {
            const foundUser = userStore.users.find((user) => user.id === currentUser.id)
            store.form.user = foundUser || currentUser
          }

          // Set source branch from user's branch if not already set
          if (!store.form.source_branch || !store.form.source_branch.id) {
            store.form.source_branch = store.form.user.branch ||
              currentUser.branch || {
              id: 0,
              name: '',
              address: '',
            }
          }
        }

        console.log('STO Dialog initialized with user/branch data')
      } catch (error) {
        console.error('Error initializing STO dialog:', error)
      }
    }
  },
)
async function editSTOdetails(row: StockTransferOrderDetails) {
  await stockStore.fetchStockByProductId(row.product.id)
  store.formSTODetails = Object.assign({}, store.formSTODetails, row);
  editSTOdetailsDialogOpen.value = true
  modeEditSTOdetails.value = 'edit'
}
function deleteSTODetails(row: StockTransferOrderDetails) {
  store.formSTODetails = Object.assign({}, store.formSTODetails, row);
  const index = store.stoDetails.findIndex(item => item.product.id === store.formSTODetails.product.id);
  if (index !== -1) {
    store.stoDetails.splice(index, 1);
  }
  console.log(row)
  store.resetFormSTODetails()
}
const formattedOrderDate = computed({
  get() {
    const dateValue = store.form.request_date
      ? new Date(store.form.request_date)
      : new Date()
    return date.formatDate(dateValue, 'YYYY/MM/DD HH:mm:ss')
  },
  set(value: string) {
    const [datePart, timePart] = value.split(' ')
    if (datePart && timePart) {
      const [year, month, day] = datePart.split('/')
      const [hour, minute, second] = timePart.split(':')

      const newDate = new Date(
        Number(year),
        Number(month) - 1,
        Number(day),
        Number(hour),
        Number(minute),
        Number(second)
      )

      store.form.request_date = newDate
    } else {
      store.form.request_date = new Date()
    }
  },
})




const columns = <QTableColumn[]>[
  {
    name: 'index',
    label: 'ลำดับ',
    field: '',
    align: 'left' as const,
    sortable: false,
  },
  {
    name: 'product.product_code',
    label: 'รหัส',
    field: (row) => (row.product ? row.product.product_code : ''),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'product.product_name',
    label: 'ชื่อสินค้า',
    field: (row) => (row.product ? row.product.product_name : ''),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'quantity',
    label: 'จำนวนสั่ง',
    field: 'quantity',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'actions',
    label: '',
    align: 'center' as const,
    field: 'actions',
    sortable: false,
  },
]

// const formattedDate = computed({
//   get() {
//     return date.formatDate(store.form.date, 'DD/MM/YYYY') // แปลงจาก Date เป็น string ในรูปแบบ YYYY-MM-DD
//   },
//   set(value: string) {
//     const newDate = new Date(value) // แปลงจาก string กลับเป็น Date
//     store.form.date = newDate // อัพเดทค่าใน store
//   },
// })
</script>

<style scoped>
.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.mini-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container-2 {
  padding-left: 5px;
  padding-right: 5px;
  background-color: white;
  border-radius: 5px;
  width: 250px;
  font-size: 12px;
  margin-left: 10px;
}

.btn-accept {
  background-color: #36b54d;
  width: 150px;
  height: 40px;
  border-radius: 10px;
  margin-right: 10px;
}

.btn-print {
  background-color: #83a7d8;
  width: 150px;
  height: 40px;
  border-radius: 10px;
  margin-right: 10px;
}

.gap-container {
  margin-bottom: 20px;
}

.gap-container-half {
  margin-bottom: 20px;
  width: 515px;
}

.dialog-container {
  border-radius: 10px;
}

.input-container-v2 {
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  background-color: #83a7d8;
}

.input-container-v3 {
  padding-left: 10px;
  padding-right: 10px;
  margin-top: 5px;
  margin-bottom: 5px;
  border-radius: 5px;
  background-color: #d6e4f6;
  width: 150px;
}

.width-column {
  width: 300px;
}

.width-column-2 {
  width: 390px;
}

.width-column-3 {
  width: 350px;
}

.column-table {
  height: 40px;
  background-color: #294888;
  color: white;
}

.column-table-2 {
  height: 40px;
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border: 2px solid #294888;
}

.container-headerhalf {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  width: 350px;
  height: 55px;
}

.containerhalf {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  width: 350px;
  height: 100px;
}

.text-po {
  margin-left: 15px;
  font-size: 30px;
}

.gap-container-left {
  margin-left: 20px;
}

.gap-container-left {
  margin-left: 20px;
}

.container-headerhalf2 {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  width: 680px;
  height: 55px;
}

.status-text {
  margin-left: 300px;
  margin-right: 30px;
}

.containerhalf2 {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  width: 680px;
  height: 100px;
}

.flex-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  /* ปรับระยะห่างระหว่าง radio */
  padding: 10px;
}

.custom-radio .q-radio__inner--truthy {
  background-color: currentColor !important;
  opacity: 1;
}

.body-table {
  background-color: white;
  margin-top: 30px;
}

:deep(.q-table thead tr) {
  background-color: #83a7d8;
}

.btn-add-2 {
  background-color: #294888;
  color: white;
  border-radius: 3px;
  margin-left: 20px;
  font-size: 13px;
  width: 100px;
}
</style>
