// import { defineStore } from "pinia"
import { acceptHMRUpdate, defineStore } from 'pinia'
import { goodsReceiptDetailService } from 'src/services/goodsreceiptdetailService'
import { goodsReceiptService } from 'src/services/goodsreceiptService'
import type { GoodsReceipt } from 'src/types/goodsReceipt'
import type { GoodsReceiptDetail } from 'src/types/goodsReceiptDatail'
import type { PaymentGoodsReceipt } from 'src/types/paymentGoodsReceipt'
// import type { Product } from 'src/types/product'

import type { PurchaseOrder } from 'src/types/purchaseOrder'
import type { Stock } from 'src/types/stock'
import type { Supplier } from 'src/types/supplier'
import { ref } from 'vue'

// const defaultForm: GoodsReceipt = {
//   id: 0,
//   code: '',
//   po: {
//     id: 0,
//     code: '',
//     supplier: {
//       id: 0,
//       supplier_number: '',
//       name: '',
//       address: '',
//       tel: '',
//       tax_number: '',
//       fax: '',
//       contact_name: '',
//       email: '',
//       type: {
//         id: 0,
//         name: '',
//       },
//       isactive: false,
//     },
//     contact: '',
//     address: '',
//     date: new Date(),
//     user: {
//       id: 0,
//       password: '',
//       name: '',
//       image: '',
//       tel: '',
//       role: '',
//       hour_work: 0,
//       sick_level: 0,
//       personal_leave: 0,
//       branch: {
//         id: 0,
//         name: '',
//         address: '',
//       },
//     },
//     po_total: 0.0,
//     tax: 7.0,
//     tax_total: 0.0,
//     status: '',
//     order_date: new Date(),
//     order_discount: 0.0,
//     note: '',
//     order_total: 0.0,
//     receive_total: 0.0,
//     product_price_tax: '',
//     order_discount_tax: '',
//     receive_status: '',
//     purchase_order_item: [
//       {
//         id: 0,
//         purchase_order: {
//           id: 0,
//           code: '',
//         } as PurchaseOrder,
//         product: {
//           id: 0,
//           product_code: '',
//           product_name: '',
//         } as Product,
//         quantity: 0,
//         unit_price: 0,
//         total_price: 0,
//       },
//     ],
//   },
//   distributor: {
//     id: 0,
//     supplier_number: '',
//     name: '',
//     address: '',
//     tel: '',
//     tax_number: '',
//     contact_name: '',
//     fax: '',
//     email: '',
//     type: {
//       id: 0,
//       name: '',
//     },
//     isactive: false,
//   },
//   po_date: new Date(),
//   po_code: '',
//   date_document: new Date(),
//   receive_date: new Date(),
//   po_total: 0,
//   tax: 0,
//   tax_total: 0,
//   gr_total: 0,
//   tax_invoice_number: '',
//   status: '',
//   user: {
//     id: 0,
//     password: '',
//     name: '',
//     image: '',
//     tel: '',
//     role: '',
//     hour_work: 0,
//     sick_level: 0,
//     personal_leave: 0,
//     branch: {
//       id: 0,
//       name: '',
//       address: '',
//     },
//   },
//   product_price_tax: '',
//   order_discount_tax: '',
//   note: '',

// }

export const useGoodsReceiptStore = defineStore('gr', {
  state: () => ({
    form: {} as GoodsReceipt,
    formGoodsReceiptDetail: {} as GoodsReceiptDetail,
    formEditGoodsReceiptDetail: {} as GoodsReceiptDetail,
    currentGrId: 0,
    formLatestGRDetail: {} as GoodsReceiptDetail,
    formPayment: {} as PaymentGoodsReceipt,
    gr: [] as GoodsReceipt[],
    grDetailsEdited: [] as GoodsReceiptDetail[],
    grDetails: [] as GoodsReceiptDetail[],
    deletedIds: [] as number[],
    grPrepare: [] as GoodsReceipt[],
    grOnProcess: [] as GoodsReceipt[],
    grAll: [] as GoodsReceipt[],
    po: {} as PurchaseOrder,
    distributor: {} as Supplier,
    payment: [] as PaymentGoodsReceipt[],
    products: [] as Stock[],
    currentStatus: '',
    searchTextDialog: ref(''),
    selectedFilterDialog: ref(''),
  }),

  getters: {
    getGoodsReceipts: (s) => s.gr,
  },

  actions: {
    async fetchAllGoodsReceipt() {
      try {
        this.gr = await goodsReceiptService.getAll()
      } catch (error) {
        console.error('Error fetching gr:', error)
      }
    },
    async fetchGoodsReceiptByStatus() {
      try {
        // if (status == 'เตรียมรายการ') {
        this.grPrepare = await goodsReceiptService.getByStatus('เตรียมรายการ')
        // } else if (status == 'กำลังดำเนินการ') {
        this.grOnProcess = await goodsReceiptService.getByStatus('ดำเนินการ')
        this.grAll = await goodsReceiptService.getByStatus('เสร็จสมบูรณ์')
        // }
      } catch (error) {
        console.error('Error fetching gr:', error)
      }
    },

    async fetchGoodsReceiptById(id: number) {
      try {
        const gr = await goodsReceiptService.getById(id)
        return (this.form = gr)
      } catch (error) {
        console.error(`Error fetching gr ${id}:`, error)
      }
    },

    async fetchDetailsByProductId(productId: number, branchId: number) {
      try {
        const latestGRDetail = await goodsReceiptDetailService.getDetailsById(productId, branchId)
        return (this.formLatestGRDetail = latestGRDetail)
      } catch (error) {
        console.error(`Error fetching gr ${productId}:`, error)
      }
    },
    async fetchDetailsByGR(id: number) {
      try {
        const grDetail = await goodsReceiptDetailService.getDetailsByGR(id)
        return (this.grDetails = grDetail)
      } catch (error) {
        console.error(`Error fetching gr ${id}:`, error)
      }
    },
    async fetchProductByDistributor(branchId: number, id: number) {
      try {
        const productList = await goodsReceiptService.getProductByDistrbutor(branchId, id, this.searchTextDialog,
          this.selectedFilterDialog,
        )
        return (this.products = productList)
      } catch (error) {
        console.error(`Error fetching gr ${id}:`, error)
      }
    },
    async createFromDistributor(gr: GoodsReceipt) {
      try {
        const newGoodsReceipt = await goodsReceiptService.createFromDistributor(gr)
        this.form = newGoodsReceipt
      } catch (error) {
        console.error('Error creating goods receipt from distributor:', error)
        // Re-throw the error so it can be handled by the calling component
      }
    },

    async createFromPO(POId: number) {
      try {
        const newGoodsReceipt = await goodsReceiptService.createFromPO(POId, this.form)
        this.form = newGoodsReceipt
      } catch (error) {
        console.error('Error adding gr:', error)
      }
    },
    async updateGr(updatedGr: GoodsReceipt) {
      try {
        const updated = await goodsReceiptService.update(updatedGr.id, updatedGr)
        if (updated) {
          this.form = updated
        }
      } catch (error) {
        console.error(`Error updating order ${updatedGr.id}:`, error)
      }
    },
    async addProduct(grDetails: GoodsReceiptDetail[], grId: number) {
      try {
        await goodsReceiptDetailService.addProduct(grDetails, grId)
      } catch (error) {
        console.error('Error adding gr:', error)
      }
    },
    async updateGRDetailsFromPo(grDetails: GoodsReceiptDetail[], grId: number) {
      try {
        await goodsReceiptDetailService.updateGRDetailsFromPO(grDetails, grId)
      } catch (error) {
        console.error('Error adding gr:', error)
      }
    },
    async removeGr(GrId: number) {
      try {
        await goodsReceiptService.delete(GrId)
        this.gr = this.gr.filter((o) => o.id !== GrId)
      } catch (error) {
        console.error(`Error deleting gr ${GrId}:`, error)
      }
    },
    resetForm() {
      this.form = {} as GoodsReceipt
    },
    resetFormGRDetails() {
      this.formGoodsReceiptDetail = {} as GoodsReceiptDetail
    },
    resetFormLatestGRDetail() {
      this.formLatestGRDetail = {} as GoodsReceiptDetail
    }
  },
})
// HMR Support (Hot Module Replacement)
if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useGoodsReceiptStore, import.meta.hot))
}
