import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { ProductConversion } from "../Models/ProductConversion";
import { Product } from "../Models/Product";
import { Stock } from "../Models/Stock";
import { QueryRunner } from "typeorm";

export class ProductConversionController {
  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const conversionRepository = AppDataSource.getRepository(ProductConversion);
      const conversions = await conversionRepository.find({
        relations: ["fromProduct", "toProduct"]
      });
      res.status(200).json(conversions);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const conversionRepository = AppDataSource.getRepository(ProductConversion);
      const conversion = await conversionRepository.findOne({
        where: { id: Number(id) },
        relations: ["fromProduct", "toProduct"]
      });
      if (conversion) {
        res.status(200).json(conversion);
      } else {
        res.status(404).json({ message: "Conversion not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async create(req: Request, res: Response): Promise<void> {
    const { fromProductId, toProductId, conversionRate } = req.body;
    try {
      const conversionRepository = AppDataSource.getRepository(ProductConversion);
      const productRepository = AppDataSource.getRepository(Product);
      const [fromProduct, toProduct] = await Promise.all([
        productRepository.findOne({ where: { id: Number(fromProductId) } }),
        productRepository.findOne({ where: { id: Number(toProductId) } })
      ]);
      if (!fromProduct || !toProduct) {
        res.status(400).json({ 
          message: "Invalid products",
          errors: {
            fromProduct: fromProduct ? null : "From product not found",
            toProduct: toProduct ? null : "To product not found"
          }
        });
        return;
      }
      const existingConversion = await conversionRepository.findOne({
        where: { 
          fromProductId: Number(fromProductId),
          toProductId: Number(toProductId)
        }
      });
      if (existingConversion) {
        existingConversion.conversionRate = Number(conversionRate);
        const updatedConversion = await conversionRepository.save(existingConversion);
        res.status(200).json({
          message: "Conversion rate updated",
          conversion: updatedConversion
        });
        return;
      }
      const newConversion = conversionRepository.create({
        fromProductId: Number(fromProductId),
        toProductId: Number(toProductId),
        conversionRate: Number(conversionRate)
      });
      const savedConversion = await conversionRepository.save(newConversion);
      res.status(201).json(savedConversion);
    } catch (error) {
      res.status(400).json({ message: "Error creating conversion", error });
    }
  }

  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const conversionRepository = AppDataSource.getRepository(ProductConversion);
    try {
      const conversion = await conversionRepository.findOne({ where: { id: Number(id) } });
      if (conversion) {
        conversionRepository.merge(conversion, req.body);
        const updatedConversion = await conversionRepository.save(conversion);
        res.status(200).json(updatedConversion);
      } else {
        res.status(404).json({ message: "Conversion not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating conversion" });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const conversionRepository = AppDataSource.getRepository(ProductConversion);
    try {
      const result = await conversionRepository.delete(Number(id));
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "Conversion not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

public async breakProduct(req: Request, res: Response): Promise<void> {
    const conversionId = Number(req.params.id);
    const toId = Number(req.body.toId);
    const quantity = Number(req.body.quantity);
    const branchId = Number(req.body.branchId);

    if (!toId || !quantity || !branchId) {
      res.status(400).json({ message: "Missing required fields: toId, quantity, or branchId" });
      return;
    }

    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const conversionRepository = queryRunner.manager.getRepository(ProductConversion);
      const stockRepository = queryRunner.manager.getRepository(Stock);
      const productRepository = queryRunner.manager.getRepository(Product);

      const conversion = await conversionRepository.findOne({ where: { id: conversionId } });
      if (!conversion) {
        res.status(404).json({ message: "Conversion not found" });
        await queryRunner.rollbackTransaction();
        return;
      }

      const fromId = conversion.fromProductId;

      const [fromProduct, toProduct] = await Promise.all([
        productRepository.findOne({ where: { id: fromId } }),
        productRepository.findOne({ where: { id: toId } })
      ]);

      if (!fromProduct || !toProduct) {
        res.status(404).json({ 
          message: "Product not found", 
          details: {
            fromProduct: fromProduct ? "Found" : "Not found",
            toProduct: toProduct ? "Found" : "Not found"
          }
        });
        await queryRunner.rollbackTransaction();
        return;
      }

      const fromStock = await stockRepository.findOne({
        where: { product: { id: fromId }, branch: { id: branchId } }
      });

      const toStock = await stockRepository.findOne({
        where: { product: { id: toId }, branch: { id: branchId } }
      });

      if (!fromStock || !toStock) {
        res.status(404).json({ message: "Stock not found" });
        await queryRunner.rollbackTransaction();
        return;
      }

      if (fromStock.remaining < quantity) {
        res.status(400).json({ 
          message: "Insufficient stock",
          available: fromStock.remaining,
          requested: quantity
        });
        await queryRunner.rollbackTransaction();
        return;
      }

      const convertedQuantity = quantity * conversion.conversionRate;

      fromStock.remaining -= quantity;
      toStock.remaining += convertedQuantity;

      fromStock.status = fromStock.remaining === 0
        ? "สินค้าหมด"
        : fromStock.remaining < fromProduct.stock_min
        ? "สินค้าใกล้หมด"
        : "สินค้าคงอยู่";

      toStock.status = toStock.remaining === 0
        ? "สินค้าหมด"
        : toStock.remaining < toProduct.stock_min
        ? "สินค้าใกล้หมด"
        : "สินค้าคงอยู่";

      await stockRepository.save([fromStock, toStock]);
      await queryRunner.commitTransaction();

      res.status(200).json({
        message: "Product conversion successful",
        fromProduct: { id: fromId, quantity: -quantity, remaining: fromStock.remaining, status: fromStock.status },
        toProduct: { id: toId, quantity: convertedQuantity, remaining: toStock.remaining, status: toStock.status }
      });
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error("Error during product conversion:", error);
      res.status(500).json({ 
        message: "Error during product conversion", 
        error: error instanceof Error ? { 
          name: error.name,
          message: error.message,
          stack: error.stack
        } : error 
      });
    } finally {
      await queryRunner.release();
    }
  }

  public async combineProduct(req: Request, res: Response): Promise<void> {
    const fromId = Number(req.params.id);
    const toId = Number(req.body.toId);
    const quantity = Number(req.body.quantity);
    const branchId = Number(req.body.branchId);

    if (!toId || !quantity || !branchId) {
      res.status(400).json({ message: "Missing required fields: toId, quantity, or branchId" });
      return;
    }

    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const conversionRepository = queryRunner.manager.getRepository(ProductConversion);
      const stockRepository = queryRunner.manager.getRepository(Stock);

      const conversion = await conversionRepository.findOne({
        where: { fromProductId: toId, toProductId: fromId }
      });

      if (!conversion) {
        res.status(404).json({ message: "Conversion not found" });
        await queryRunner.rollbackTransaction();
        return;
      }

      const fromStock = await stockRepository.findOne({
        where: { product: { id: fromId }, branch: { id: branchId } }
      });

      if (!fromStock) {
        res.status(404).json({ message: "Source product stock not found" });
        await queryRunner.rollbackTransaction();
        return;
      }

      const requiredQuantity = quantity * conversion.conversionRate;

      if (fromStock.remaining < requiredQuantity) {
        res.status(400).json({ 
          message: "Insufficient stock",
          available: fromStock.remaining,
          required: requiredQuantity,
          requested: quantity
        });
        await queryRunner.rollbackTransaction();
        return;
      }

      const toStock = await stockRepository.findOne({
        where: { product: { id: toId }, branch: { id: branchId } }
      });

      if (!toStock) {
        res.status(404).json({ message: "Target product stock not found" });
        await queryRunner.rollbackTransaction();
        return;
      }

      fromStock.remaining -= requiredQuantity;
      toStock.remaining += quantity;

      fromStock.status = fromStock.remaining === 0
        ? "สินค้าหมด"
        : fromStock.remaining < toStock.product.stock_min
        ? "สินค้าใกล้หมด"
        : "สินค้าคงอยู่";

      await stockRepository.save([fromStock, toStock]);
      await queryRunner.commitTransaction();

      res.status(200).json({
        message: "Product combination successful",
        fromProduct: { id: fromId, quantity: -requiredQuantity, remaining: fromStock.remaining },
        toProduct: { id: toId, quantity: quantity, remaining: toStock.remaining }
      });
    } catch (error) {
      await queryRunner.rollbackTransaction();
      res.status(500).json({ message: "Error during product combination", error });
    } finally {
      await queryRunner.release();
    }
  }
}
