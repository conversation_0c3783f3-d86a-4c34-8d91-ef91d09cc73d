import { defineStore } from 'pinia'

export const useDialogGRDetails = defineStore('dialog2', {
  state: () => ({
    isOpen2: false,
    mode: '',
    modeDistribu: '',
    modePO: '',
    modePM: '',
    modeInfo: '',
    modePD: '',
    modeCU: '',
    isOpenPO: false,
    isOpenDistributor: false,
    isOpenPM: false,
    isOpenInfo: false,
    isOpenPD: false,
    isOpenCU: false,
  }),
  actions: {
    open2(mode = 'add') {
      this.isOpen2 = true
      this.mode = mode
    },
    close2() {
      this.isOpen2 = false
    },
    openPO(modePO = 'addPO') {
      this.isOpenPO = true
      this.modePO = modePO
    },
    closePO() {
      this.isOpenPO = false
    },
    openDistributor(modeDistribu = 'selectDistribu') {
      this.isOpenDistributor = true
      this.modeDistribu = modeDistribu
    },
    closeDistributor() {
      this.isOpenDistributor = false
    },
    openPM(modePM = 'addPM') {
      this.isOpenPM = true
      this.modePM = modePM
    },
    closePM() {
      this.isOpenPM = false
    },
    openPD(modePD = 'addPD') {
      this.isOpenPD = true
      this.modePD = modePD
    },
    closePD() {
      this.isOpenPD = false
      this.modePD = ''
    },
    openInfo(modeInfo = 'info') {
      this.isOpenInfo = true
      this.modeInfo = modeInfo
    },
    closeInfo() {
      this.isOpenInfo = false
    },
    openCU(modeCU = 'addCU') {
      this.isOpenCU = true
      this.modeCU = modeCU
    },
    closeCU() {
      this.isOpenCU = false
    },
  },
})
