import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, OneToOne, <PERSON>inColumn, ManyToOne } from "typeorm";
import { Product } from "./Product";
import { Branch } from "./Branch";
import { GoodsReceiptDetails } from "./GoodsReceiptDetails";

@Entity()
export class Stock {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne(() => Product, { onDelete: "CASCADE" })
  @JoinColumn()
  product!: Product;

  @ManyToOne(() => Branch, { onDelete: "CASCADE" })
  @JoinColumn()
  branch!: Branch;

  // @OneToOne(() => GoodsReceiptDetails, grDetail => grDetail.stock)
  // gr_detail!: GoodsReceiptDetails;

  @Column({ type: "integer" })
  remaining!: number;

  @Column({ type: "text", nullable: true })
  status?: string;

  @Column({ type: "text", nullable: true })
  lot_number!: string;

  @Column({ type: 'float', nullable: true })
  cost_unit?: number;

  @Column({ default: true, nullable: true })
  is_active!: boolean;
}
