import { Router } from "express";
import { GoodsReceiptDetailsController } from "../Controllers/GoodsReceiptDetails";

const router = Router();
const controller = new GoodsReceiptDetailsController();

router.get("/", controller.getAll.bind(controller));
router.get("/:id", controller.getById.bind(controller));
router.get("/product/:productId/branch/:branchId", controller.getByProductId.bind(controller));
router.get("/gr/:grId", controller.getByGr.bind(controller));
router.post("/:grId", controller.create.bind(controller));
router.put("/:grId", controller.updateGRDetailsFromPO.bind(controller)); //uopdate grdetails ที่ create from po
router.put("/:id", controller.update.bind(controller));
router.delete("/:id", controller.delete.bind(controller));

export default router;
