<template>
  <UserNavigation></UserNavigation>
  <div class="q-pa-md">
    <div class="container q-pa-md">
      <div class="text-h6 q-ml-md row justify-between items-center">
        <div>ตารางการเข้างาน</div>
        <q-btn color="black" flat icon="arrow_back" @click="goBack" />
      </div>
      <!-- Attendance table -->
      <div class="q-pa-md">
        <q-table
          :rows="attendanceRows"
          :columns="columns"
          row-key="id"
          :pagination="{ rowsPerPage: 0 }"
          flat
          bordered
          separator="vertical"
          class="attendance-table"
        >
          <!-- Custom header for date columns -->
          <template v-slot:header="props">
            <q-tr :props="props">
              <q-th key="user" :props="props" class="user-column">name</q-th>
              <q-th
                v-for="dateObj in dateRange.dates"
                :key="dateObj.date"
                :props="props"
                class="text-center date-column"
              >
                {{ getDayName(dateObj.date) }}
                <div>{{ formatDateHeader(dateObj.date) }}</div>
              </q-th>
            </q-tr>
          </template>

          <!-- Custom body for all cells -->
          <template v-slot:body="props">
            <q-tr :props="props" class="cursor-pointer" @click="() => onRowClick(props.row)">
              <!-- User column with avatar and name -->
              <q-td key="user" :props="props" class="user-column">
                <div class="row items-center no-wrap">
                  <q-avatar size="40px" class="q-mr-sm">
                    <img :src="getUserImage(props.row.id)" />
                  </q-avatar>
                  <div>
                    <div class="text-weight-bold">{{ props.row.name }}</div>
                    <div class="text-caption">{{ getUserRole(props.row.id) }}</div>
                  </div>
                </div>
              </q-td>

              <!-- Date columns with status badges -->
              <q-td
                v-for="dateObj in dateRange.dates"
                :key="dateObj.date"
                :props="props"
                class="text-center date-column"
              >
                <q-badge
                  :class="{
                    'status-badge': true,
                    'holiday-badge': props.row[dateObj.date] === 'holiday',
                    'leave-badge': props.row[dateObj.date].includes('leave'),
                    'work-badge': props.row[dateObj.date].includes('ชม.'),
                    'absent-badge': props.row[dateObj.date] === '-',
                  }"
                >
                  {{ getStatusLabel(props.row[dateObj.date]) }}
                </q-badge>
              </q-td>
            </q-tr>
          </template>
        </q-table>

        <!-- Loading indicator -->
        <div v-if="attendanceSummaryStore.loading" class="text-center q-pa-md">
          <q-spinner color="primary" size="2em" />
          <div class="q-mt-sm">โหลดข้อมูล...</div>
        </div>

        <!-- Error message -->
        <div v-if="attendanceSummaryStore.error" class="text-center q-pa-md text-negative">
          {{ attendanceSummaryStore.error }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import UserNavigation from 'src/components/userNavigation.vue'
import { date } from 'quasar'
import type { QTableColumn } from 'quasar'
import { useAttendanceSummaryStore } from 'src/stores/attendance-summary'
import type { AttendanceSummary } from 'src/types/attendancesummary'
import { UserService } from 'src/services/userService'
import { useUserStore } from 'src/stores/userStore'

const route = useRoute()
const router = useRouter()
const attendanceSummaryStore = useAttendanceSummaryStore()
const userStore = useUserStore()
const userRoles = ref<Record<number, string>>({})

// Get date range from query parameters
const dateRange = ref({
  from: (route.query.from as string) || date.formatDate(new Date(), 'YYYY/MM/DD'),
  to: (route.query.to as string) || date.formatDate(new Date(), 'YYYY/MM/DD'),
  dates: [] as { date: string }[],
})

// Attendance rows for the table
const attendanceRows = ref<
  Array<{
    id: number
    name: string
    [key: string]: string | number // For dynamic date columns
  }>
>([])

// Table columns
const columns = computed<QTableColumn[]>(() => {
  const cols: QTableColumn[] = [
    {
      name: 'user',
      label: 'ชื่อ',
      field: 'name',
      align: 'left',
    },
  ]

  // Add date columns
  dateRange.value.dates.forEach((dateObj) => {
    cols.push({
      name: dateObj.date,
      label: formatDateHeader(dateObj.date),
      field: dateObj.date,
      align: 'center',
    })
  })

  return cols
})

// Generate array of dates between from and to
const generateDateRange = () => {
  const dates = []
  const start = new Date(dateRange.value.from)
  const end = new Date(dateRange.value.to)

  for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
    dates.push({
      date: date.formatDate(d, 'YYYY-MM-DD'),
    })
  }

  dateRange.value.dates = dates
}

// Fetch attendance summary data
const fetchAttendanceSummary = async () => {
  try {
    const data = await attendanceSummaryStore.fetchAttendanceSummary(
      dateRange.value.from,
      dateRange.value.to,
    )
    console.log('Attendance summary data:', data)

    // Process data for table rows
    processAttendanceData(data)
  } catch (error) {
    console.error('Error fetching attendance summary:', error)
  }
}

// Process attendance data for table display
const processAttendanceData = (data: AttendanceSummary[]) => {
  attendanceRows.value = data.map((user) => {
    const row: {
      id: number
      name: string
      [key: string]: string | number // For dynamic date columns
    } = {
      id: user.userId,
      name: user.userName,
    }

    // Add attendance status for each date
    dateRange.value.dates.forEach((dateObj) => {
      const dateData = user.dates[dateObj.date]
      if (dateData) {
        if (dateData.isHoliday) {
          row[dateObj.date] = 'holiday'
        } else if (dateData.leave) {
          row[dateObj.date] = `${dateData.leave} leave`
        } else if (dateData.worked) {
          row[dateObj.date] = `${dateData.hours || 0} ชม.`
        } else {
          row[dateObj.date] = '-'
        }
      } else {
        row[dateObj.date] = '-'
      }
    })

    return row
  })
}

// Format date for header display
const formatDateHeader = (dateStr: string): string => {
  return date.formatDate(new Date(dateStr), 'DD/MM')
}

// Get day name
const getDayName = (dateStr: string): string => {
  return date.formatDate(new Date(dateStr), 'ddd')
}

// Go back to summary page
const goBack = () => {
  router.push({ name: 'user-summary' }).catch((error) => {
    console.error('Navigation error:', error)
  })
}

// Watch for changes in date range
watch(
  () => [dateRange.value.from, dateRange.value.to],
  async () => {
    generateDateRange()
    await fetchAttendanceSummary()
  },
  { immediate: false },
)

onMounted(async () => {
  // Generate date range array
  generateDateRange()
  // Fetch attendance summary data
  await fetchAttendanceSummary()
})

// Helper function to get display label for status
const getStatusLabel = (status: string | number): string => {
  if (status === 'holiday') return 'วันหยุด'
  if (status.toString().includes('leave')) {
    if (status.toString().includes('sick')) return 'ลาป่วย'
    return 'ลากิจ'
  }
  if (status.toString().includes('ชม.')) return 'มาทำงาน'
  if (status === '-') return 'วันทำงาน'
  return status.toString()
}

const userImages = ref<Record<number, string | null>>({})

// Load user images
const loadUserImages = async () => {
  for (const row of attendanceRows.value) {
    if (row.id) {
      try {
        const imageUrl = await UserService.getUserImageById(row.id)
        userImages.value[row.id] = imageUrl
      } catch (error) {
        console.error(`Failed to load image for user ${row.id}:`, error)
      }
    }
  }
}

// Get user image or fallback to default
const getUserImage = (userId: number): string => {
  return userImages.value[userId] || 'https://cdn.quasar.dev/img/avatar.png'
}

// Fetch user roles
const fetchUserRoles = async () => {
  if (!userStore.users || userStore.users.length === 0) {
    await userStore.fetchUsers()
  }

  for (const row of attendanceRows.value) {
    if (row.id) {
      const user = userStore.users.find((u) => u.id === row.id)
      if (user && user.role) {
        userRoles.value[row.id] = user.role
      } else {
        userRoles.value[row.id] = 'ประจำ' // Default role
      }
    }
  }
}

// Get user role by ID
const getUserRole = (userId: number): string => {
  return userRoles.value[userId] || 'ประจำ'
}

// Call fetchUserRoles after processing attendance data
watch(
  attendanceRows,
  async () => {
    await fetchUserRoles()
    await loadUserImages() // Keep the existing image loading
  },
  { immediate: false },
)

const onRowClick = (row: { id: number }) => {
  console.log('✅ Clicked row ID:', row.id)

  void router
    .push({
      name: 'user-attendance-detail',
      params: { id: row.id },
    })
    .catch((error) => {
      console.error('❌ Navigation error:', error)
      void router.push(`/user/attendance/${row.id}`)
    })
}
</script>

<style scoped>
.container {
  background-color: white;
  border-radius: 10px;
}

.attendance-table {
  border-radius: 8px;
  overflow: hidden;
}

.attendance-table :deep(th) {
  background-color: #e1edea !important;
  font-weight: bold;
  text-align: center;
}

.attendance-table :deep(td) {
  padding: 12px;
}

.user-column {
  background-color: #f5f5f5;
  min-width: 180px;
}

.date-column {
  min-width: 120px;
  text-align: center;
}

.status-badge {
  width: 100%;
  padding: 8px;
  border-radius: 4px;
  font-weight: bold;
  display: inline-block;
}

.holiday-badge {
  background-color: #b53638;
  color: white;
}

.work-badge {
  background-color: #1f6336;
  color: white;
}

.absent-badge {
  background-color: #439e62;
  color: white;
}

.text-caption {
  font-size: 0.7rem;
  color: #666;
}
</style>
