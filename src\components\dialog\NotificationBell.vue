<template>
  <div>
    <!-- ไอคอนกระดิ่ง -->
    <q-icon name="notifications" size="32px" class="cursor-pointer" @click="showDialog = true">
      <q-badge v-if="totalNotifications > 0" color="red" floating rounded>
        {{ totalNotifications }}
      </q-badge>
    </q-icon>

    <!-- Dialog แจ้งเตือน -->
    <q-dialog v-model="showDialog">
      <q-card class="card-container">
        <q-card-section class="card-header">
          <div class="row justify-between">
            <div style="font-size: 18px" class="flex flex-center">
              <q-icon name="notifications" size="32px" class="q-mr-sm" />การแจ้งเตือน
            </div>
            <q-btn icon="close" v-close-popup flat rounded />
          </div>
        </q-card-section>

        <q-card-section class="card-body">
          <div v-if="totalNotifications === 0" class="text-center">ไม่มีการแจ้งเตือน</div>
          <div v-else>
            <q-list class="q-gutter-md">
              <!-- Stock Alerts -->
              <q-card
                v-for="product in lowStockProducts"
                :key="`stock-${product.id}`"
                :class="product.remaining === 0 ? 'bg-red text-black' : 'bg-orange'"
                class="q-pa-md"
              >
                <q-item>
                  <q-item-section avatar>
                    <q-icon
                      :name="product.remaining === 0 ? 'warning' : 'priority_high'"
                      :color="product.remaining === 0 ? 'black' : 'black'"
                      size="md"
                    />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="text-bold">
                      สินค้า {{ product.product.product_name }}
                    </q-item-label>
                    <q-item-label caption>
                      สต็อก: {{ product.remaining }} / {{ product.product.stock_min }}
                    </q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-badge
                      :color="product.remaining === 0 ? 'red' : 'orange'"
                      class="text-black text-bold"
                      style="font-size: 14px"
                    >
                      {{ product.remaining === 0 ? 'หมดแล้ว' : 'ใกล้หมด' }}
                    </q-badge>
                  </q-item-section>
                </q-item>
              </q-card>

              <!-- Expiration Alerts -->
              <q-card
                v-for="product in expirationAlerts"
                :key="`expiry-${product.id}`"
                :class="product.isExpired ? 'bg-red text-black' : 'bg-yellow text-black'"
                class="q-pa-md"
              >
                <q-item>
                  <q-item-section avatar>
                    <q-icon
                      :name="product.isExpired ? 'dangerous' : 'schedule'"
                      :color="product.isExpired ? 'black' : 'black'"
                      size="md"
                    />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="text-bold">
                      สินค้า {{ product.product_name }}
                    </q-item-label>
                    <q-item-label caption>
                      วันหมดอายุ: {{ formatDate(product.expirationDate) }}
                      <span v-if="!product.isExpired">
                        (อีก {{ product.daysUntilExpiration }} วัน)
                      </span>
                    </q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-badge
                      :color="product.isExpired ? 'red' : 'yellow'"
                      class="text-black text-bold"
                      style="font-size: 14px"
                    >
                      {{ product.isExpired ? 'หมดอายุแล้ว' : 'ใกล้หมดอายุ' }}
                    </q-badge>
                  </q-item-section>
                </q-item>
              </q-card>
            </q-list>
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useStockStore } from 'src/stores/stock'
import { productService } from 'src/services/productService'
import type { ProductExpirationAlert } from 'src/types/product'

const showDialog = ref(false)
const stockStore = useStockStore()
const expirationAlertsData = ref<ProductExpirationAlert[]>([])

// คำนวณหาสินค้าที่ใกล้หมดหรือหมดแล้ว
const lowStockProducts = computed(() => {
  return stockStore.notificationData.lowStockProducts
})

// คำนวณหาสินค้าที่หมดอายุหรือใกล้หมดอายุ
const expirationAlerts = computed(() => {
  return expirationAlertsData.value
})

// คำนวณจำนวนการแจ้งเตือนทั้งหมด
const totalNotifications = computed(() => {
  return lowStockProducts.value.length + expirationAlerts.value.length
})

// ฟังก์ชันจัดรูปแบบวันที่
const formatDate = (dateString?: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('th-TH')
}

// ดึงข้อมูลการแจ้งเตือนหมดอายุ
const fetchExpirationAlerts = async () => {
  try {
    expirationAlertsData.value = await productService.getExpirationAlerts()
  } catch (error) {
    console.error('Error fetching expiration alerts:', error)
  }
}

// ดึงข้อมูลเมื่อ Component ถูกโหลด
onMounted(async () => {
  await stockStore.fetchNotifications()
  await fetchExpirationAlerts()
})
</script>

<style scoped>
.card-header {
  background-color: #91d2c1;
  color: black;
}

.card-body {
  background-color: #e1edea;
}

.card-container {
  width: 100%;
  max-width: 500px;
}
</style>
