import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
} from "typeorm";
import { User } from "./User";

@Entity()
export class Attendance {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: "date" })
  date!: string;

  @Column({ type: "time" })
  clock_in!: string;

  @Column({ type: "time", nullable: true })
  clock_out?: string;

  @Column({ type: "float", nullable: true })
  work_duration?: number;

  @Column({ nullable: true })
  note?: string;

  @Column({ default: "Present" })
  status!: string;

  // Location fields for check-in
  @Column({ type: "float", nullable: true })
  check_in_latitude?: number;

  @Column({ type: "float", nullable: true })
  check_in_longitude?: number;

  @Column({ type: "float", nullable: true })
  check_in_accuracy?: number;

  // Location fields for check-out
  @Column({ type: "float", nullable: true })
  check_out_latitude?: number;

  @Column({ type: "float", nullable: true })
  check_out_longitude?: number;

  @Column({ type: "float", nullable: true })
  check_out_accuracy?: number;

  @ManyToOne(() => User, (user) => user.attendances)
  user!: User;

  @CreateDateColumn()
  created_at!: Date;
}
