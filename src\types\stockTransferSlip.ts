import type { Branch } from "./branch";
import type { StockTransferOrder } from "./stockTransferOrder";
import type { StockTransferSlipDetails } from "./stockTransferSlipDetails";
import type { user } from "./user";

export interface StockTransferSlip {
  id: number;
  sto: StockTransferOrder;
  code: string;
  sto_code: string;
  status: string;
  source_branch: Branch;
  destination_branch: Branch;
  transfer_date: string;
  user: user;
  note: string;
  sts_price?: number;
  sts_details: StockTransferSlipDetails[];
}

