let jwt: any;
try {
  jwt = require("jsonwebtoken");
} catch (error) {
  console.warn("⚠️ jsonwebtoken not available, using fallback");
  jwt = null;
}

// JWT Secret - In production, this should be in environment variables
const JWT_SECRET =
  process.env.JWT_SECRET || "your-super-secret-jwt-key-change-in-production";

// Token expiration time (4 hours)
const TOKEN_EXPIRATION = "4h";

export interface JwtPayload {
  userId: number;
  name: string;
  role: string;
  iat?: number;
  exp?: number;
}

/**
 * Generate JWT token for user
 */
export const generateToken = (user: {
  id: number;
  name: string;
  role: string;
}): string => {
  if (!jwt) {
    // Fallback token when JWT is not available
    const payload = {
      userId: user.id,
      name: user.name,
      role: user.role,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 4 * 60 * 60, // 4 hours
    };
    return (
      "fallback." + Buffer.from(JSON.stringify(payload)).toString("base64")
    );
  }

  const payload: JwtPayload = {
    userId: user.id,
    name: user.name,
    role: user.role,
  };

  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: TOKEN_EXPIRATION,
  });
};

/**
 * Verify JWT token
 */
export const verifyToken = (token: string): JwtPayload => {
  try {
    if (!jwt) {
      // Fallback verification for development
      if (token.startsWith("fallback.")) {
        const payload = JSON.parse(
          Buffer.from(token.split(".")[1], "base64").toString()
        );
        if (payload.exp && payload.exp > Math.floor(Date.now() / 1000)) {
          return payload as JwtPayload;
        }
        throw new Error("Token expired");
      }
      throw new Error("Invalid token format");
    }
    return jwt.verify(token, JWT_SECRET) as JwtPayload;
  } catch (error) {
    throw new Error("Invalid or expired token");
  }
};

/**
 * Decode JWT token without verification (for debugging)
 */
export const decodeToken = (token: string): JwtPayload | null => {
  try {
    return jwt.decode(token) as JwtPayload;
  } catch (error) {
    return null;
  }
};

/**
 * Check if token is expired
 */
export const isTokenExpired = (token: string): boolean => {
  try {
    const decoded = decodeToken(token);
    if (!decoded || !decoded.exp) return true;

    const currentTime = Math.floor(Date.now() / 1000);
    return decoded.exp < currentTime;
  } catch (error) {
    return true;
  }
};

/**
 * Get token expiration time in milliseconds
 */
export const getTokenExpirationTime = (token: string): number | null => {
  try {
    const decoded = decodeToken(token);
    if (!decoded || !decoded.exp) return null;

    return decoded.exp * 1000; // Convert to milliseconds
  } catch (error) {
    return null;
  }
};
