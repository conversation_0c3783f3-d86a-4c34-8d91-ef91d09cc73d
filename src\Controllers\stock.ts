import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { Stock } from "../Models/Stock";
import { Product } from "../Models/Product";
import { Branch } from "../Models/Branch";

export class StockController {
  // 📌 ดึง Stock ทั้งหมด พร้อม Product และ Branch
  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const stockRepository = AppDataSource.getRepository(Stock);
      const stocks = await stockRepository.find({ relations: ["product", "branch", "product.manufacturer", "product.distributor", "product.product_group", "product.special_report_group"] });
      res.status(200).json(stocks);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  // 📌 ดึง Stock ตาม ID พร้อมข้อมูล Product และ Branch
  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const stockRepository = AppDataSource.getRepository(Stock);
      const stock = await stockRepository.findOne({
        where: { id: Number(id) },
        relations: ["product", "branch"]
      });

      if (stock) {
        res.status(200).json(stock);
      } else {
        res.status(404).json({ message: "Stock not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getByProductId(req: Request, res: Response): Promise<void> {
    const { productId } = req.params;
    try {
      const stockRepository = AppDataSource.getRepository(Stock);
      const stock = await stockRepository.findOne({
        where: { product: { id: Number(productId) } },
        relations: ["product"]
      });

      if (stock) {
        res.status(200).json(stock);
      } else {
        res.status(404).json({ message: "Stock not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  // 📌 ดึง Stock ตาม Branch ID
  public async getByBranchId(req: Request, res: Response): Promise<void> {
    const { branchId } = req.params;
    try {
      const stockRepository = AppDataSource.getRepository(Stock);
      const stocks = await stockRepository.find({
        where: { branch: { id: Number(branchId) } },
        relations: ["product", "branch"]
      });

      if (stocks.length === 0) {
        res.status(404).json({ message: "No stock found for this branch" });
        return;
      }

      // นับจำนวน Stock ตาม Status
      const countByStatus = stocks.reduce((acc, stock) => {
        const statusKey = stock.status ?? "ไม่ระบุสถานะ"; // ถ้า status เป็น undefined ให้ใช้ค่าเริ่มต้น
        acc[statusKey] = (acc[statusKey] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);


      res.status(200).json({ stocks, countByStatus });
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getAllByFilter(req: Request, res: Response): Promise<void> {
    const { search, filter, branch, distributor } = req.body;

    try {
      const stockRepository = AppDataSource.getRepository(Stock);

      const query = stockRepository
        .createQueryBuilder("stock")
        .leftJoinAndSelect("stock.product", "product")
        .leftJoinAndSelect("product.distributor", "distributor")
        .leftJoinAndSelect("product.manufacturer", "manufacturer")
        .leftJoinAndSelect("product.product_group", "product_group")
        .leftJoinAndSelect("product.special_report_group", "special_report_group")
        .leftJoinAndSelect("stock.branch", "branch")
        .where("stock.remaining > 0")
        .andWhere("stock.is_active = true");

      // ✅ Handle search functionality
      if (search) {
        if (filter) {
          // กรณีมีทั้ง search และ filter: ค้นหาใน field ที่กำหนด
          const allowedFilters = [
            "product_code",
            "product_name",
            "unit",
            "barcode",
            "generic_group",
            "storage_location"
          ];

          if (allowedFilters.includes(filter)) {
            query.andWhere(`product.${filter} LIKE :search`, { search: `%${search}%` });
          } else {
            res.status(400).json({ message: `Invalid filter field: ${filter}` });
            return;
          }
        } else {
          // กรณีมี search แต่ไม่มี filter: ค้นหาแบบทั่วไปในทุก field
          query.andWhere(
            `(
              product.product_code LIKE :search OR
              product.product_name LIKE :search OR
              product.unit LIKE :search OR
              product.barcode LIKE :search OR
              product.generic_group LIKE :search OR
              product.storage_location LIKE :search OR
              stock.lot_number LIKE :search OR
              branch.name LIKE :search
            )`,
            { search: `%${search}%` }
          );
        }
      }

      // ✅ เงื่อนไข branch
      if (branch) {
        query.andWhere("branch.id = :branch", { branch });
      }
      if (distributor) {
        query.andWhere("distributor.id = :distributor", { distributor });
      }

      const stocks = await query.getMany();
      res.status(200).json(stocks);

    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getStockSummaryByProduct(req: Request, res: Response): Promise<void> {
    const { search, product_id } = req.body;

    try {
      const query = AppDataSource.getRepository(Stock)
        .createQueryBuilder("stock")
        .leftJoin("stock.product", "product")
        .leftJoin("stock.branch", "branch")
        .select("branch.id", "branch_id")
        .addSelect("branch.name", "branch_name")
        .addSelect("SUM(stock.remaining)", "total_remaining")
        .where("product.id = :productId", { productId: product_id })
        .andWhere("stock.remaining > 0")
        .andWhere("stock.is_active = true");

      // ✅ เงื่อนไขค้นหาด้วยชื่อสาขา
      if (search) {
        query.andWhere("branch.name LIKE :search", { search: `%${search}%` });
      }

      const result = await query
        .groupBy("branch.id")
        .addGroupBy("branch.name")
        .getRawMany();

      res.status(200).json(result);
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Server error", error });
    }
  }



  // 📌 เพิ่ม Stock ใหม่
  public async create(req: Request, res: Response): Promise<void> {
    const { productId, branchId, remaining, status } = req.body;
    try {
      const stockRepository = AppDataSource.getRepository(Stock);
      const productRepository = AppDataSource.getRepository(Product);
      const branchRepository = AppDataSource.getRepository(Branch);

      const product = await productRepository.findOne({ where: { id: Number(productId) } });
      const branch = await branchRepository.findOne({ where: { id: Number(branchId) } });

      if (!product || !branch) {
        res.status(400).json({ message: "Invalid product or branch" });
        return;
      }

      const newStock = new Stock();
      newStock.product = product;
      newStock.branch = branch;
      newStock.remaining = remaining ?? 0;
      newStock.status = status ?? "สินค้าหมด";

      const savedStock = await stockRepository.save(newStock);
      res.status(201).json(savedStock);
    } catch (error) {
      res.status(400).json({ message: "Error creating Stock" });
    }
  }

  // 📌 อัปเดต Stock
  // 📌 อัปเดต Stock
  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const { remaining, branchId, lot_number } = req.body;

    try {
      const stockRepository = AppDataSource.getRepository(Stock);
      const branchRepository = AppDataSource.getRepository(Branch);
      const productRepository = AppDataSource.getRepository(Product);

      const stock = await stockRepository.findOne({
        where: { id: Number(id) },
        relations: ["product"]
      });

      if (!stock) {
        res.status(404).json({ message: "Stock not found" });
        return;
      }

      if (branchId) {
        const branch = await branchRepository.findOne({ where: { id: Number(branchId) } });
        if (branch) {
          stock.branch = branch;
        } else {
          res.status(400).json({ message: "Invalid branch ID" });
          return;
        }
      }

      if (remaining !== undefined) {
        stock.remaining = remaining;

        // ดึงค่า stock_min จาก product
        const product = await productRepository.findOne({ where: { id: stock.product.id } });
        if (product) {
          if (stock.remaining === 0) {
            stock.status = "สินค้าหมด";
            // stock.is_active = false
          } else if (stock.remaining < product.stock_min) {
            stock.status = "สินค้าใกล้หมด";
          } else {
            stock.status = "สินค้าคงอยู่";
          }
        }
      }
      // ✅ อัปเดต lot_number ถ้ามีส่งมา
      if (lot_number !== undefined) {
        stock.lot_number = lot_number;
      }
      const updatedStock = await stockRepository.save(stock);
      res.status(200).json(updatedStock);
    } catch (error) {
      res.status(400).json({ message: "Error updating Stock" });
    }
  }


  // 📌 ลบ Stock
  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const stockRepository = AppDataSource.getRepository(Stock);
      const result = await stockRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "Stock not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }
}
