import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { PurchaseOrder } from "../Models/PurchaseOrder";
import { Supplier } from "../Models/Supplier";
import { User } from "../Models/User";
import { Branch } from "../Models/Branch";
import { StockTransferOrder } from "../Models/StockTransferOrder";
import { Stock } from "../Models/Stock";
import { GoodsReceipt } from "../Models/GoodsReceipt";
import { GoodsReceiptDetails } from "../Models/GoodsReceiptDetails";

export class GoodsReceiptController {

  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const grRepository = AppDataSource.getRepository(GoodsReceipt);
      const gr = await grRepository.find({
        relations: ["po", "distributor", "branch", "user", "gr_details", "gr_details.product", "gr_details.product.distributor", "payment"]
      });
      res.status(200).json(gr);
    } catch (error) {
      console.error("Error in getAll:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const grRepository = AppDataSource.getRepository(GoodsReceipt);
      const gr = await grRepository.findOne({
        where: { id: Number(id) },
        relations: ["po", "po.user", "distributor", "branch", "user", "gr_details", "gr_details.product", "gr_details.product.distributor", "payment"]
      });
      if (gr) {
        res.status(200).json(gr);
      } else {
        res.status(404).json({ message: "GR not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getByStatus(req: Request, res: Response): Promise<void> {
    const { status } = req.params;
    try {
      const grRepository = AppDataSource.getRepository(GoodsReceipt);
      const list = await grRepository.find({
        where: { status }, // status: 'เตรียมรายการ' หรือ 'กำลังดำเนินการ'
        relations: ["po", "branch", "distributor", "gr_details", "user", "gr_details.product", "payment"],
        order: { date_document: "DESC" }
      });
      res.status(200).json(list);
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getProductByDistrbutor(req: Request, res: Response): Promise<void> {
    const { id, branchId } = req.params;
    const { search, filter } = req.query;

    try {
      const stockRepository = AppDataSource.getRepository(Stock);

      const queryBuilder = stockRepository
        .createQueryBuilder("stock")
        .leftJoinAndSelect("stock.product", "product")
        .leftJoinAndSelect("product.distributor", "distributor")
        .leftJoinAndSelect("stock.branch", "branch")
        .where("distributor.id = :distributorId", { distributorId: Number(id) })
        .andWhere("branch.id = :branchId", { branchId: Number(branchId) });

      if (filter && search && typeof filter === 'string' && typeof search === 'string') {
        // ถ้าเลือก filter เฉพาะเจาะจง (เช่น product_code หรือ barcode)
        queryBuilder.andWhere(`product.${filter} LIKE :search`, { search: `%${search}%` });
      } else if (search && typeof search === 'string') {
        // ถ้าเป็นการ search แบบทั่วไปในหลายช่อง
        queryBuilder.andWhere(`
          product.product_name LIKE :search OR
          product.product_code LIKE :search OR
          product.generic_name LIKE :search OR
          product.barcode LIKE :search OR
          product.generic_group LIKE :search
        `, { search: `%${search}%` });
      }

      const result = await queryBuilder.getMany();

      if (result.length > 0) {
        res.status(200).json(result);
      } else {
        res.status(404).json({ message: "No products found" });
      }

    } catch (error) {
      console.error("Error fetching products:", error);
      res.status(500).json({ message: "Server error" });
    }
  }



  public async getAllByFilter(req: Request, res: Response): Promise<void> {
    const { search, filter, startDate, endDate } = req.body;

    try {
      const purchaseOrderRepository = AppDataSource.getRepository(PurchaseOrder);

      const query = purchaseOrderRepository
        .createQueryBuilder("purchase_order")
        .leftJoinAndSelect("purchase_order.supplier", "supplier")
        .leftJoinAndSelect("purchase_order.user", "user")
        .where("1=1");

      // กรองตามคำค้นหาที่ส่งมา
      if (search) {
        query.andWhere(
          `(
            purchase_order.code LIKE :search
            OR user.name LIKE :search
            OR purchase_order.status LIKE :search
          )`,
          { search: `%${search}%` }
        );
      }

      if (filter && search) {
        query.andWhere(`purchase_order.${filter} LIKE :search`, {
          search: `%${search}%`,
        });
      }

      if (startDate) {
        query.andWhere("DATE(purchase_order.date) >= :startDate", {
          startDate: startDate,
        });
      }

      if (endDate) {
        query.andWhere("DATE(purchase_order.date) <= :endDate", {
          endDate: endDate,
        });
      }

      const purchaseOrders = await query.getMany();
      res.status(200).json(purchaseOrders);
    } catch (error) {
      console.error("Error filtering purchase orders:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async createFromDistributor(req: Request, res: Response): Promise<void> { //createfromdistributor
    const userRepository = AppDataSource.getRepository(User);
    const branchRepository = AppDataSource.getRepository(Branch);
    const grRepository = AppDataSource.getRepository(GoodsReceipt);
    const distributorRepository = AppDataSource.getRepository(Supplier);

    const {
      distributor,
      branch,
      date_document,
      tax_invoice_date,
      credit_date,
      credit_days,
      user,
      tax = 0.00,
      tax_total = 0.00,
      gr_total = 0.00,
      note = '',
      gr_details = [] // ถ้าไม่ได้ส่งมาก็ให้เป็น array ว่าง
    } = req.body;

    try {
      const userEntity = await userRepository.findOneByOrFail({ id: user.id });
      const distributorEntity = await distributorRepository.findOneByOrFail({ id: distributor.id });
      const branchEntity = await branchRepository.findOneByOrFail({ id: branch.id });


      // แปลงวันที่เป็น YYYYMMDD
      const formattedDate = new Date(date_document).toISOString().slice(0, 10).replace(/-/g, "");

      // ดึง ID ล่าสุด เพื่อใช้ในการ gen code
      const lastGR = await grRepository.find({
        order: { id: "DESC" },
        take: 1
      });

      const lastId = lastGR.length > 0 ? lastGR[0].id + 1 : 1;

      // สร้างรหัส
      const generatedCode = `GR-${formattedDate}-${lastId}`;

      // สร้าง STO ใหม่
      const newGR = grRepository.create({
        code: generatedCode,
        user: userEntity,
        distributor: distributorEntity,
        branch: branchEntity,
        date_document: date_document,
        tax_invoice_date: tax_invoice_date,
        credit_date: credit_date,
        credit_days: credit_days,
        tax,
        tax_total,
        status: 'เตรียมรายการ', // กำหนดสถานะเริ่มต้น
        gr_total,
        gr_details, // จะเป็น [] ถ้าไม่ได้ส่งมา
        note: note
      });

      if (credit_date && credit_days !== undefined) {
        const baseDate = new Date(credit_date); // สมมุติ credit_date ส่งมาถูกต้องในรูปแบบ Date หรือ ISO string
        baseDate.setDate(baseDate.getDate() + Number(credit_days));
        newGR.credit_due_date = baseDate;
      }

      const grDetailsWithRelation = gr_details.map((detail: any) => {
        return {
          ...detail,
          gr: newGR, // เชื่อมความสัมพันธ์กลับ
        };
      });

      newGR.gr_details = grDetailsWithRelation;
      const savedGR = await grRepository.save(newGR);

      res.status(201).json(savedGR);
    } catch (error) {
      console.error("Error creating GR:", error);
      res.status(500).json({ message: "Failed to create GR", error });
    }
  }

  public async createGRByPO(req: Request, res: Response): Promise<void> {
    const grRepository = AppDataSource.getRepository(GoodsReceipt);
    const poRepository = AppDataSource.getRepository(PurchaseOrder);
    const stockRepository = AppDataSource.getRepository(Stock);
    const userRepository = AppDataSource.getRepository(User);
    const branchRepository = AppDataSource.getRepository(Branch);
    const { poId } = req.params
    const {
      branch,
      user,
      date_document,
      tax_invoice_date,
      credit_date,
      credit_days,
      vat_percent = 0.00,
      tax,
      tax_total,
    } = req.body;
    const po = await poRepository.findOneOrFail({
      where: { id: Number(poId) },
      relations: ["supplier", "branch", "user", "purchase_order_items", "purchase_order_items.product"]
    });
    try {
      const userEntity = await userRepository.findOneByOrFail({ id: user.id });
      const branchEntity = await branchRepository.findOneByOrFail({ id: branch.id });
      // แปลงวันที่เป็น YYYYMMDD
      const formattedDate = new Date().toISOString().slice(0, 10).replace(/-/g, "");

      // ดึง ID ล่าสุด เพื่อใช้ในการ gen code
      const lastGR = await grRepository.find({
        order: { id: "DESC" },
        take: 1
      });

      const lastId = lastGR.length > 0 ? lastGR[0].id + 1 : 1;

      // สร้างรหัส
      const generatedCode = `GR-${formattedDate}-${lastId}-${po.code}`;

      // สร้าง STS ใหม่
      const newGR = grRepository.create({
        po: po,
        code: generatedCode,
        po_code: po.code,
        distributor: po.supplier,
        branch: branchEntity,
        date_document: date_document,
        credit_date: credit_date,
        tax_invoice_date: tax_invoice_date,
        credit_days: credit_days,
        user: userEntity,
        vat_percent: vat_percent,
        tax: tax,
        tax_total: tax_total,
        gr_total: 0.00,
        status: 'เตรียมรายการ', // กำหนดสถานะเริ่มต้น
      });

      if (credit_date && credit_days !== undefined) {
        const baseDate = new Date(credit_date); // สมมุติ credit_date ส่งมาถูกต้องในรูปแบบ Date หรือ ISO string
        baseDate.setDate(baseDate.getDate() + Number(credit_days));
        newGR.credit_due_date = baseDate;
      }
      // บันทึกลง DB เพื่อให้ได้ newSts.id
      const savedGR = await grRepository.save(newGR);

      // แปลง detail และผูกกลับหา parent
      const grDetailRepository = AppDataSource.getRepository(GoodsReceiptDetails);

      // let tax_total = 0.00
      let gr_details_total = 0.00
      const grDetails = await Promise.all(
        po.purchase_order_items.map(async (detail) => {
          const lotStock = await stockRepository.findOneOrFail({
            where: {
              // branch: { id: po.supplier.id },
              product: { id: detail.product.id }
            },
            relations: ["product", "branch"]
          });
          // tax_total += (detail.quantity * detail.unit_price)
          // let tax = ((detail.quantity * detail.unit_price) * 7) / 100
          gr_details_total += detail.total_price
          return grDetailRepository.create({
            gr: newGR,
            product: detail.product,
            receive_quantity: detail.quantity,
            receive_unit: detail.product.unit,
            receive_price_before_tax: detail.unit_price,
            receive_price_after_discount: detail.unit_price,
            free_quantity: 0,
            has_discount: false,
            has_free: false,
            production_number: '',
            mfg_date: '',
            exp_date: '',
            free_unit: detail.product.unit,
            total_receive_quantity: detail.quantity,  //+free_quantity
            cost_unit: detail.unit_price,
            total_price_product: (detail.quantity * detail.unit_price),
            lot_number_before: lotStock.lot_number ?? '',
            po_details: detail,
            gr_total: detail.total_price //รวม ตาราง
          });
        })
      );

      // let tax = (tax_total * 7) / 100
      // tax_total += tax
      // save ทั้ง array

      await grDetailRepository.save(grDetails);
      savedGR.gr_details = grDetails;
      await grRepository.update(savedGR.id, {
        gr_details_total: gr_details_total,
      });

      //upate gr in po
      // await grRepository.update(sto.id, {
      //   sts: savedSTS
      // });

      const response = {
        ...savedGR,
        gr_details: grDetails.map(detail => ({
          id: detail.id,
          product: detail.product,
          receive_quantity: detail.receive_quantity,
          receive_unit: detail.receive_unit,
          receive_price_before_tax: detail.receive_price_before_tax,
          receive_price_after_discount: detail.receive_price_after_discount,
          free_quantity: detail.free_quantity,
          free_unit: detail.free_unit,
          total_receive_quantity: detail.total_receive_quantity,  //+free_quantity
          cost_unit: detail.cost_unit,
          total_price_product: detail.total_price_product,
          lot_number_before: detail.lot_number_before,
        }))
      };
      res.status(201).json(response);

      // res.status(201).json(savedSTS);
    } catch (error) {
      console.error("Error creating GR:", error);
      res.status(500).json({ message: "Failed to create GR", error });
    }
  }


  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const grRepository = AppDataSource.getRepository(GoodsReceipt);
    try {
      const gr = await grRepository.findOne({ where: { id: Number(id) } });
      if (gr) {
        grRepository.merge(gr, req.body);
        const updatedGR = await grRepository.save(gr);
        res.status(200).json(updatedGR);
      } else {
        res.status(404).json({ message: "Goods Receipt not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating Goods Receipt", error });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const grRepository = AppDataSource.getRepository(GoodsReceipt);
    try {
      const result = await grRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "Goods Receipt not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }
}