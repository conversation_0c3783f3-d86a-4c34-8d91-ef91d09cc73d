import axios from 'axios'
import type { Stock } from 'src/types/stock'
import type { StockTransferSlip } from 'src/types/stockTransferSlip'

const API_URL = 'http://localhost:3000/sts'
// เปลี่ยนให้ตรงกับ backend ของคุณ


export const stockTransFerSlipService = {
  // ดึงข้อมูลรายการ GoodsReceipt ทั้งหมด
  async getAll(): Promise<StockTransferSlip[]> {
    const response = await axios.get(API_URL)
    return response.data
  },

  // ดึงข้อมูล Purchase Order ตาม ID
  async getById(id: number): Promise<StockTransferSlip> {
    const response = await axios.get(`${API_URL}/${id}`)
    return response.data
  },

  async getProductByBranch(productId: number): Promise<Stock[]> {
    const response = await axios.get(`${API_URL}/product/${productId}`)
    return response.data
  },

  // สร้าง Goods Receipt ใหม่จาก Distributor
  async create(sts: StockTransferSlip): Promise<StockTransferSlip> {
    const payload = {
      ...sts,
    }
    const response = await axios.post(API_URL, payload)
    return response.data
  },

  async createFromSTO(stoId: number): Promise<StockTransferSlip> {
    const response = await axios.post(`${API_URL}/createByPO/${stoId}`)
    return response.data
  },

  async update(id: number, sts: Partial<StockTransferSlip>): Promise<StockTransferSlip> {
    const response = await axios.put(`${API_URL}/${id}`, sts)
    return response.data
  },

  async updateSTSCreateBySTO(id: number, sts: Partial<StockTransferSlip>): Promise<StockTransferSlip> {
    const response = await axios.put(`${API_URL}/${id}`, sts)
    return response.data
  },

  async delete(id: number): Promise<void> {
    await axios.delete(`${API_URL}/${id}`)
  },

  // async filter(search: string, filter: string, startDate: string, endDate: string): Promise<GoodsReceipt[]> {
  //   try {
  //     const response = await axios.post(`${API_URL}/filter`, {
  //       search,
  //       filter,
  //       startDate,
  //       endDate
  //     })
  //     return response.data
  //   } catch (error) {
  //     console.error("Error filtering purchase orders", error)
  //     throw error
  //   }
  // },
}
