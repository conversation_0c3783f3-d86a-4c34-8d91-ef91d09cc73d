import { Router } from "express";
import { BranchReceiveController } from "../Controllers/BranchReceive";

const router = Router();
const controller = new BranchReceiveController();

router.get("/", controller.getAll.bind(controller));
router.get("/:id", controller.getById.bind(controller));
router.post("/createBySTS/:stsId", controller.createBRBySTS.bind(controller));
router.put("/:id", controller.update.bind(controller));
router.delete("/:id", controller.delete.bind(controller));
router.post("/filter", controller.getAllByFilter.bind(controller));

export default router;
