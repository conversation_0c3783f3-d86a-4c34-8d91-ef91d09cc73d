<template>
  <q-page class="flex flex-center fade-in-page">
    <q-card flat class="q-pa-md login-card slide-in-card">
      <div class="left-side">
        <div class="logo-container">
          <img src="/icon.png/header.png" alt="Logo" class="logo animated-logo" />
        </div>
        <div class="company-name">สุขถาวรโอสถ</div>
        <div class="company-tagline">SUKTHAVORN OSOT</div>
      </div>
      <!-- ด้านขวา -->
      <div class="right-side">
        <div class="login-container">
          <q-card-section>
            <img src="/icon.png/user.png" alt="Logo" style="width: 64px" />
          </q-card-section>

          <q-card-section>
            <div class="text-h6">กรุณาเข้าสู่ระบบ</div>
          </q-card-section>

          <q-card-section class="login-section">
            <div class="text-bold q-mb-sm">ชื่อผู้ใช้งาน *</div>
            <q-input
              class="input-container q-mb-md"
              v-model="username"
              dense
              borderless
              label="กรุณากรอกชื่อผู้ใช้งาน"
            />
            <div class="text-bold q-mb-sm">รหัสผ่าน *</div>
            <q-input
              dense
              borderless
              v-model="password"
              label="รหัสผ่าน"
              type="password"
              class="input-container"
            />
          </q-card-section>

          <q-card-actions class="btn-section">
            <q-btn flat label="เข้าสู่ระบบ" class="btn-login animated-btn" @click="handleLogin" />
          </q-card-actions>
        </div>
      </div>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from 'src/stores/authStore'
import { useUserStore } from 'src/stores/userStore'

const username = ref('')
const password = ref('')
const router = useRouter()
const authStore = useAuthStore()
const userStore = useUserStore()

// ฟังก์ชันสำหรับ login
const handleLogin = async () => {
  if (username.value && password.value) {
    console.log('Logging in user:', username.value)

    try {
      // เรียกใช้ฟังก์ชัน login จาก auth store
      const userInfo = await authStore.login(username.value, password.value)

      // Update user store with current user info (add password field for type compatibility)
      userStore.setCurrentUser({ ...userInfo, password: '' })

      // ถ้า login สำเร็จจะไปที่หน้า home
      if (authStore.isAuthenticated) {
        await router.push('/home')
        console.log('✅ Login successful, redirecting to home')
      } else {
        console.error('Authentication failed')
        alert('ชื่อผู้ใช้งานหรือรหัสผ่านไม่ถูกต้อง')
      }
    } catch (error) {
      console.error('Login failed:', error)
      alert('ไม่สามารถเข้าสู่ระบบได้')
    }
  } else {
    console.log('Please fill in both fields')
    alert('กรุณากรอกชื่อผู้ใช้งานและรหัสผ่าน')
  }
}
</script>

<style scoped>
.q-page {
  position: relative;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.q-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('/icon.png/background.png') center center / cover no-repeat;
  opacity: 1;
  z-index: 0;
  pointer-events: none;
}

.q-page > * {
  position: relative;
  z-index: 1;
}
@keyframes gradientMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.fade-in-page {
  animation: fadeIn 1.2s ease;
}

.login-card {
  display: flex;
  width: 100%;
  max-width: 1000px;
  height: 100%;
  max-height: 600px;
  border-radius: 18px;
  background-color: rgba(255, 255, 255, 0.7);
  position: relative;
  /* เงาเข้มและฟุ้งมากขึ้นแบบชัดเจน */
  box-shadow:
    0 16px 48px 0 rgba(31, 38, 135, 0.32),
    0 6px 32px 0 rgba(0, 0, 0, 0.28),
    0 2px 8px 0 rgba(0, 0, 0, 0.18);
}

.slide-in-card {
  animation: slideInUp 1s cubic-bezier(0.23, 1, 0.32, 1);
}

.login-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 40%;
  height: 100%;
  background-color: rgba(255, 255, 255, 1);
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}

.left-side {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  max-width: 400px;
  background-color: transparent;
  z-index: 1;
}

.right-side {
  flex: 1;
  display: flex;
  background-color: transparent;
}

.login-container {
  width: 565px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  height: 100%;
  max-height: 560px;
  margin-left: 16px;
  z-index: 1;
  background-color: #fff;
}

.logo-container {
  margin-bottom: 20px;
}

.logo {
  width: 120px;
  height: 120px;
}

.animated-logo {
  animation: logoPop 1.2s cubic-bezier(0.23, 1, 0.32, 1);
}

.company-name {
  font-size: 48px;
  font-weight: bold;
  margin-top: 10px;
}

.company-tagline {
  font-size: 18px;
  font-weight: bold;
}

.q-card-section {
  margin-bottom: 20px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: #e1edea;
  border-radius: 8px;
}

.login-section {
  width: 100%;
  max-width: 400px;
}

.btn-login {
  background-color: #91d2c1;
  color: black;
  width: 100%;
  max-width: 200px;
  border-radius: 8px;

  transition:
    transform 0.2s,
    box-shadow 0.2s;
}

.animated-btn {
  animation: btnFadeIn 1.5s 0.5s backwards;
}

.btn-login:hover {
  transform: scale(1.07);
  box-shadow: 0 6px 18px rgba(145, 210, 193, 0.3);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(60px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes logoPop {
  0% {
    transform: scale(0.7) rotate(-10deg);
    opacity: 0;
  }
  60% {
    transform: scale(1.1) rotate(3deg);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(0);
    opacity: 1;
  }
}

@keyframes btnFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.btn-section {
  width: 100%;
  max-width: 200px;
}
</style>
