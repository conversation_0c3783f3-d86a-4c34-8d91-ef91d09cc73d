import { AppDataSource } from "../Config/db";
import { WorkingSchedule } from "../Models/WorkingSchedule";
import { User } from "../Models/User";

export class WorkingScheduleService {
  private static workingScheduleRepository = AppDataSource.getRepository(WorkingSchedule);
  private static userRepository = AppDataSource.getRepository(User);

  // Get user's active working schedule
  static async getUserSchedule(userId: number): Promise<WorkingSchedule | null> {
    try {
      const schedule = await this.workingScheduleRepository.findOne({
        where: { 
          user: { id: userId },
          is_active: true 
        },
        relations: ["user"],
      });
      
      return schedule;
    } catch (error) {
      console.error("Error getting user schedule:", error);
      return null;
    }
  }

  // Create default schedule for a new user
  static async createDefaultSchedule(userId: number): Promise<WorkingSchedule | null> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId }
      });

      if (!user) {
        throw new Error("User not found");
      }

      const schedule = this.workingScheduleRepository.create({
        user,
        standard_check_in_time: "09:00:00",
        standard_check_out_time: "17:00:00",
        late_threshold_minutes: 5,
        early_threshold_minutes: 30,
        is_active: true,
      });

      return await this.workingScheduleRepository.save(schedule);
    } catch (error) {
      console.error("Error creating default schedule:", error);
      return null;
    }
  }

  // Update user's working schedule
  static async updateSchedule(
    userId: number, 
    scheduleData: {
      standard_check_in_time: string;
      standard_check_out_time: string;
      late_threshold_minutes?: number;
      early_threshold_minutes?: number;
    }
  ): Promise<WorkingSchedule | null> {
    try {
      let schedule = await this.getUserSchedule(userId);

      if (!schedule) {
        // Create new schedule if none exists
        const user = await this.userRepository.findOne({
          where: { id: userId }
        });

        if (!user) {
          throw new Error("User not found");
        }

        schedule = this.workingScheduleRepository.create({
          user,
          ...scheduleData,
          late_threshold_minutes: scheduleData.late_threshold_minutes || 5,
          early_threshold_minutes: scheduleData.early_threshold_minutes || 30,
          is_active: true,
        });
      } else {
        // Update existing schedule
        schedule.standard_check_in_time = scheduleData.standard_check_in_time;
        schedule.standard_check_out_time = scheduleData.standard_check_out_time;
        schedule.late_threshold_minutes = scheduleData.late_threshold_minutes || schedule.late_threshold_minutes;
        schedule.early_threshold_minutes = scheduleData.early_threshold_minutes || schedule.early_threshold_minutes;
        schedule.updated_at = new Date();
      }

      return await this.workingScheduleRepository.save(schedule);
    } catch (error) {
      console.error("Error updating schedule:", error);
      return null;
    }
  }

  // Get all active schedules
  static async getAllSchedules(): Promise<WorkingSchedule[]> {
    try {
      return await this.workingScheduleRepository.find({
        where: { is_active: true },
        relations: ["user"],
        order: { created_at: "DESC" },
      });
    } catch (error) {
      console.error("Error getting all schedules:", error);
      return [];
    }
  }

  // Delete (deactivate) a schedule
  static async deleteSchedule(scheduleId: number): Promise<boolean> {
    try {
      const schedule = await this.workingScheduleRepository.findOne({
        where: { id: scheduleId }
      });

      if (!schedule) {
        return false;
      }

      schedule.is_active = false;
      schedule.updated_at = new Date();
      await this.workingScheduleRepository.save(schedule);
      
      return true;
    } catch (error) {
      console.error("Error deleting schedule:", error);
      return false;
    }
  }

  // Check if user has a working schedule
  static async hasSchedule(userId: number): Promise<boolean> {
    try {
      const schedule = await this.getUserSchedule(userId);
      return schedule !== null;
    } catch (error) {
      console.error("Error checking if user has schedule:", error);
      return false;
    }
  }

  // Get schedule with formatted times for display
  static async getFormattedSchedule(userId: number): Promise<any> {
    try {
      const schedule = await this.getUserSchedule(userId);
      
      if (!schedule) {
        return null;
      }

      return {
        id: schedule.id,
        userId: schedule.user.id,
        userName: schedule.user.name,
        checkInTime: schedule.standard_check_in_time.substring(0, 5), // Remove seconds
        checkOutTime: schedule.standard_check_out_time.substring(0, 5), // Remove seconds
        lateThresholdMinutes: schedule.late_threshold_minutes,
        earlyThresholdMinutes: schedule.early_threshold_minutes,
        isActive: schedule.is_active,
        createdAt: schedule.created_at,
        updatedAt: schedule.updated_at,
      };
    } catch (error) {
      console.error("Error getting formatted schedule:", error);
      return null;
    }
  }
}
