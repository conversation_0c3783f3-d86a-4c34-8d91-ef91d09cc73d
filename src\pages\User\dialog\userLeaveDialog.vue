<template>
  <q-dialog v-model="isDialogOpen" persistent>
    <q-card style="max-width: 500px; width: 100%">
      <!-- Header Section -->
      <q-card-section class="row items-center justify-between q-pb-none">
        <div class="text-h6 text-weight-bold">ลงวันลา</div>
        <q-btn icon="close" @click="closeDialog" flat rounded />
      </q-card-section>

      <q-separator class="q-mt-md" />

      <!-- Content Section -->
      <q-card-section style="max-height: 70vh" class="scroll q-pa-md">
        <!-- Details Header -->
        <div class="gap-container">
          <div class="text-white shadow-2 container-header">รายละเอียด</div>
          <div class="shadow-2 container">
            <!-- Leave Type Section -->
            <div class="q-mb-md">
              <div class="text-subtitle2 q-mb-sm text-weight-medium">ประเภทการลา</div>
              <div class="row q-gutter-md">
                <q-radio
                  v-model="leaveForm.leaveType"
                  val="personal"
                  label="ลากิจ"
                  color="red"
                  class="custom-radio"
                />
                <q-radio
                  v-model="leaveForm.leaveType"
                  val="sick"
                  label="ลาป่วย"
                  color="blue"
                  class="custom-radio"
                />
              </div>
            </div>

            <!-- reason Section -->
            <div class="q-mb-md">
              <div class="text-subtitle2 q-mb-sm text-weight-medium">หมายเหตุ</div>
              <q-input
                v-model="leaveForm.reason"
                type="textarea"
                outlined
                rows="4"
                placeholder="กรุณาระบุหมายเหตุ..."
                class="input-container"
                bg-color="white"
              />
            </div>
          </div>
        </div>
      </q-card-section>

      <!-- Action Buttons -->
      <q-card-actions align="center" class="q-pa-md">
        <q-btn
          label="ยืนยัน"
          @click="submitLeaveRequest"
          class="btn-confirm"
          :loading="isSubmitting"
          :disable="!isFormValid"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useQuasar } from 'quasar'
import { useLeaveRequestStore } from 'src/stores/leaveRequest'

// Define props for dialog control
interface Props {
  modelValue?: boolean
  userId?: number | null
  selectedDate?: {
    date: number
    month: number
    year: number
  } | null
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  userId: null,
  selectedDate: null,
})

// Define emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// Quasar instance for notifications
const $q = useQuasar()

// Store
const leaveRequestStore = useLeaveRequestStore()

// Dialog state
const isDialogOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// Leave request form interface
interface LeaveRequestData {
  leaveType: 'sick' | 'personal' | ''
  reason: string
}

// Form data
const leaveForm = ref<LeaveRequestData>({
  leaveType: '',
  reason: '',
})

// Loading state
const isSubmitting = ref(false)

// Helper function for timezone-safe date formatting
const formatDateToString = (year: number, month: number, date: number): string => {
  const monthStr = String(month + 1).padStart(2, '0') // month is 0-indexed
  const dayStr = String(date).padStart(2, '0')
  return `${year}-${monthStr}-${dayStr}`
}

// Form validation
const isFormValid = computed(() => {
  return leaveForm.value.leaveType !== '' && leaveForm.value.reason.trim() !== ''
})

// Methods
const closeDialog = () => {
  isDialogOpen.value = false
  resetForm()
}

const resetForm = () => {
  leaveForm.value = {
    leaveType: '',
    reason: '',
  }
}

const submitLeaveRequest = async () => {
  if (!isFormValid.value) {
    $q.notify({
      type: 'negative',
      message: 'กรุณากรอกข้อมูลให้ครบถ้วน',
      position: 'top',
    })
    return
  }

  if (!props.userId) {
    $q.notify({
      type: 'negative',
      message: 'ไม่พบข้อมูลผู้ใช้',
      position: 'top',
    })
    return
  }

  if (!props.selectedDate) {
    $q.notify({
      type: 'negative',
      message: 'กรุณาเลือกวันที่ต้องการลา',
      position: 'bottom',
    })
    return
  }

  isSubmitting.value = true

  try {
    // Format the selected date to YYYY-MM-DD using timezone-safe method
    const formattedDate = formatDateToString(
      props.selectedDate.year,
      props.selectedDate.month,
      props.selectedDate.date,
    )

    console.log('Submitting leave request for date:', formattedDate) // Debug log

    // Call the store function to create leave request
    await leaveRequestStore.createLeaveRequest(
      props.userId,
      formattedDate,
      leaveForm.value.leaveType,
      leaveForm.value.reason,
    )

    $q.notify({
      type: 'positive',
      message: 'ขอลาเรียบร้อยแล้ว',
      position: 'bottom',
    })

    // Emit success event to parent component
    emit('success')
    closeDialog()
  } catch (err) {
    console.error('Error submitting leave request:', err)
    const errorMessage = leaveRequestStore.error || 'เกิดข้อผิดพลาดในการส่งคำขอลา'
    $q.notify({
      type: 'negative',
      message: errorMessage,
      position: 'top',
    })
  } finally {
    isSubmitting.value = false
  }
}

// Watch for dialog close to reset form
watch(isDialogOpen, (newValue) => {
  if (!newValue) {
    resetForm()
  }
})
</script>

<style scoped>
.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.container-header {
  background-color: #294888;
  padding: 10px 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 45px;
  display: flex;
  align-items: center;
}

.input-container {
  background-color: white;
  border-radius: 5px;
}

.btn-confirm {
  background-color: #36b54d;
  color: white;
  width: 120px;
  border-radius: 8px;
  font-weight: bold;
}

.btn-confirm:hover {
  background-color: #2d9a42;
}

.custom-radio {
  font-weight: 500;
}

.custom-radio .q-radio__label {
  color: #333;
  font-weight: 500;
}

.gap-container {
  margin-bottom: 0;
}

.shadow-2 {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
