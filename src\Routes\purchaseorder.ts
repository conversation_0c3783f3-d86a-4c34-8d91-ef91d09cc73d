import { Router } from "express";
import { PurchaseOrderController } from "../Controllers/purchaseorder";

const router = Router();
const controller = new PurchaseOrderController();

router.get("/", controller.getAll.bind(controller));
router.get("/:id", controller.getById.bind(controller));
router.get("/:id/purchaseorderitem", controller.getPurchaseOrderItemsByPoId.bind(controller));
router.post("/", controller.create.bind(controller));
router.put("/:id", controller.update.bind(controller));
router.delete("/:id", controller.delete.bind(controller));
router.post("/filter", controller.getAllByFilter.bind(controller));

export default router;
